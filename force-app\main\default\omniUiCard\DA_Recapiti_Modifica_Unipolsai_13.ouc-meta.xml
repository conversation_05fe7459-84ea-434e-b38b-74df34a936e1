<?xml version="1.0" encoding="UTF-8"?>
<OmniUiCard xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorName>Unipolsai</authorName>
    <dataSourceConfig>{&quot;dataSource&quot;:{&quot;type&quot;:null,&quot;value&quot;:{},&quot;orderBy&quot;:{},&quot;contextVariables&quot;:[]},&quot;state0element1block_element3_0&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;AnagDetails_RecapitiDelete&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;RecAgenziaCellPersonale&quot;:&quot;{RecAgenziaCellPersonale}&quot;,&quot;RecAgenziaCellPersonalePreferred&quot;:&quot;{RecAgenziaCellPersonalePreferred}&quot;,&quot;RecAgenziaCellPersonaleUsage&quot;:&quot;{RecAgenziaCellPersonaleUsage}&quot;,&quot;RecAgenziaCellPersonaleId&quot;:&quot;{RecAgenziaCellPersonaleId}&quot;,&quot;userId&quot;:&quot;{User.userId}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;RecAgenziaCellPersonale\&quot;:\&quot;{RecAgenziaCellPersonale}\&quot;,\&quot;RecAgenziaCellPersonalePreferred\&quot;:\&quot;{RecAgenziaCellPersonalePreferred}\&quot;,\&quot;RecAgenziaCellPersonaleUsage\&quot;:\&quot;{RecAgenziaCellPersonaleUsage}\&quot;,\&quot;RecAgenziaCellPersonaleId\&quot;:\&quot;{RecAgenziaCellPersonaleId}\&quot;,\&quot;User.userId\&quot;:\&quot;{User.userId}\&quot;}&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;RecAgenziaCellPersonale&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:1},{&quot;name&quot;:&quot;RecAgenziaCellPersonalePreferred&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:3},{&quot;name&quot;:&quot;RecAgenziaCellPersonaleUsage&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:5},{&quot;name&quot;:&quot;RecAgenziaCellPersonaleId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:7},{&quot;name&quot;:&quot;User.userId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:9}]},&quot;state0element1block_element3block_element0_0&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;AnagDetails_RecapitiDelete&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;recordId&quot;:&quot;{recordId}&quot;,&quot;userId&quot;:&quot;{User.userId}&quot;,&quot;Recapito&quot;:&quot;{RecAgenziaCellPersonale}&quot;,&quot;Preferred&quot;:&quot;{RecAgenziaCellPersonalePreferred}&quot;,&quot;Usage&quot;:&quot;{RecAgenziaCellPersonaleUsage}&quot;,&quot;idContatto&quot;:&quot;{RecAgenziaCellPersonaleId}&quot;,&quot;tipoContatto&quot;:&quot;CELL&quot;,&quot;username&quot;:&quot;{inserisci.userInfo.username}&quot;,&quot;userIdReqBody&quot;:&quot;{inserisci.userInfo.userId}&quot;,&quot;compagnia&quot;:&quot;{inserisci.userInfo.compagnia}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;recordId\&quot;:\&quot;{recordId}\&quot;,\&quot;userId\&quot;:\&quot;{userId}\&quot;,\&quot;RecAgenziaCellPersonale\&quot;:\&quot;{RecAgenziaCellPersonale}\&quot;,\&quot;RecAgenziaCellPersonalePreferred\&quot;:\&quot;{RecAgenziaCellPersonalePreferred}\&quot;,\&quot;RecAgenziaCellPersonaleUsage\&quot;:\&quot;{RecAgenziaCellPersonaleUsage}\&quot;,\&quot;RecAgenziaCellPersonaleId\&quot;:\&quot;{RecAgenziaCellPersonaleId}\&quot;,\&quot;User.userId\&quot;:\&quot;{User.userId}\&quot;,\&quot;inserisci.userInfo.username\&quot;:\&quot;{inserisci.userInfo.username}\&quot;,\&quot;inserisci.userInfo.userId\&quot;:\&quot;{inserisci.userInfo.userId}\&quot;,\&quot;inserisci.userInfo.compagnia\&quot;:\&quot;{inserisci.userInfo.compagnia}\&quot;}&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;{recordId}&quot;,&quot;id&quot;:1},{&quot;name&quot;:&quot;userId&quot;,&quot;val&quot;:&quot;{User.userId}&quot;,&quot;id&quot;:3},{&quot;name&quot;:&quot;RecAgenziaCellPersonale&quot;,&quot;val&quot;:&quot;{RecAgenziaCellPersonale}&quot;,&quot;id&quot;:4},{&quot;name&quot;:&quot;RecAgenziaCellPersonalePreferred&quot;,&quot;val&quot;:&quot;{RecAgenziaCellPersonalePreferred}&quot;,&quot;id&quot;:4},{&quot;name&quot;:&quot;RecAgenziaCellPersonaleUsage&quot;,&quot;val&quot;:&quot;{RecAgenziaCellPersonaleUsage}&quot;,&quot;id&quot;:5},{&quot;name&quot;:&quot;RecAgenziaCellPersonaleId&quot;,&quot;val&quot;:&quot;{RecAgenziaCellPersonaleId}&quot;,&quot;id&quot;:6},{&quot;name&quot;:&quot;User.userId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:4},{&quot;name&quot;:&quot;inserisci.userInfo.username&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:17},{&quot;name&quot;:&quot;inserisci.userInfo.userId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:18},{&quot;name&quot;:&quot;inserisci.userInfo.compagnia&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:19}]},&quot;state0element1block_element5_0&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;AnagDetails_RecapitiDelete&quot;,&quot;vlocityAsync&quot;:false},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[]},&quot;state0element1block_element5block_element0_0&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;AnagDetails_RecapitiDelete&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;recordId&quot;:&quot;{recordId}&quot;,&quot;userId&quot;:&quot;{User.userId}&quot;,&quot;Recapito&quot;:&quot;{RecAgenziaEmailPers}&quot;,&quot;Preferred&quot;:&quot;{RecAgenziaEmailPersPreferred}&quot;,&quot;Usage&quot;:&quot;{RecAgenziaEmailPersUsage}&quot;,&quot;idContatto&quot;:&quot;{RecAgenziaEmailPersId}&quot;,&quot;tipoContatto&quot;:&quot;MAIL&quot;,&quot;username&quot;:&quot;{inserisci.userInfo.username}&quot;,&quot;userIdReqBody&quot;:&quot;{inserisci.userInfo.userId}&quot;,&quot;compagnia&quot;:&quot;{inserisci.userInfo.compagnia}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;User.userId\&quot;:\&quot;{User.userId}\&quot;,\&quot;recordId\&quot;:\&quot;{recordId}\&quot;,\&quot;RecAgenziaEmailPers\&quot;:\&quot;{RecAgenziaEmailPers}\&quot;,\&quot;RecAgenziaEmailPersPreferred\&quot;:\&quot;{RecAgenziaEmailPersPreferred}\&quot;,\&quot;RecAgenziaEmailPersUsage\&quot;:\&quot;{RecAgenziaEmailPersUsage}\&quot;,\&quot;RecAgenziaEmailPersId\&quot;:\&quot;{RecAgenziaEmailPersId}\&quot;,\&quot;inserisci.userInfo.compagnia\&quot;:\&quot;{inserisci.userInfo.compagnia}\&quot;,\&quot;inserisci.userInfo.userId\&quot;:\&quot;{inserisci.userInfo.userId}\&quot;,\&quot;inserisci.userInfo.username\&quot;:\&quot;{inserisci.userInfo.username}\&quot;}&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;User.userId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:2},{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:3},{&quot;name&quot;:&quot;RecAgenziaEmailPers&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:8},{&quot;name&quot;:&quot;RecAgenziaEmailPersPreferred&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:9},{&quot;name&quot;:&quot;RecAgenziaEmailPersUsage&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:10},{&quot;name&quot;:&quot;RecAgenziaEmailPersId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:11},{&quot;name&quot;:&quot;inserisci.userInfo.compagnia&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:17},{&quot;name&quot;:&quot;inserisci.userInfo.userId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:18},{&quot;name&quot;:&quot;inserisci.userInfo.username&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:19}]},&quot;state0element1block_element7_0&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;AnagDetails_RecapitiDelete&quot;,&quot;vlocityAsync&quot;:false},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[]},&quot;state0element1block_element7block_element0_0&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;AnagDetails_RecapitiDelete&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;userId&quot;:&quot;{User.userId}&quot;,&quot;recordId&quot;:&quot;{recordId}&quot;,&quot;Recapito&quot;:&quot;{RecAgenziaFax}&quot;,&quot;Preferred&quot;:&quot;{RecAgenziaFaxPreferred}&quot;,&quot;RecAgenziaFaxUsage&quot;:&quot;{RecAgenziaFaxUsage}&quot;,&quot;idContatto&quot;:&quot;{RecAgenziaFaxId}&quot;,&quot;tipoContatto&quot;:&quot;FAX&quot;,&quot;username&quot;:&quot;{inserisci.userInfo.username}&quot;,&quot;userIdReqBody&quot;:&quot;{inserisci.userInfo.userId}&quot;,&quot;compagnia&quot;:&quot;{inserisci.userInfo.compagnia}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;User.userId\&quot;:\&quot;{User.userId}\&quot;,\&quot;recordId\&quot;:\&quot;{recordId}\&quot;,\&quot;RecAgenziaCellPersonaleId\&quot;:\&quot;{RecAgenziaCellPersonaleId}\&quot;,\&quot;RecAgenziaCellPersonaleUsage\&quot;:\&quot;{RecAgenziaCellPersonaleUsage}\&quot;,\&quot;RecAgenziaCellPersonalePreferred\&quot;:\&quot;{RecAgenziaCellPersonalePreferred}\&quot;,\&quot;RecAgenziaCellPersonale\&quot;:\&quot;{RecAgenziaCellPersonale}\&quot;,\&quot;RecAgenziaFaxId\&quot;:\&quot;{RecAgenziaFaxId}\&quot;,\&quot;RecAgenziaFaxUsage\&quot;:\&quot;{RecAgenziaFaxUsage}\&quot;,\&quot;RecAgenziaFaxPreferred\&quot;:\&quot;{RecAgenziaFaxPreferred}\&quot;,\&quot;RecAgenziaFax\&quot;:\&quot;{RecAgenziaFax}\&quot;,\&quot;inserisci.userInfo.username\&quot;:\&quot;{inserisci.userInfo.username}\&quot;,\&quot;inserisci.userInfo.userId\&quot;:\&quot;{inserisci.userInfo.userId}\&quot;,\&quot;inserisci.userInfo.compagnia\&quot;:\&quot;{inserisci.userInfo.compagnia}\&quot;}&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;User.userId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:1},{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:3},{&quot;name&quot;:&quot;RecAgenziaCellPersonaleId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:8},{&quot;name&quot;:&quot;RecAgenziaCellPersonaleUsage&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:9},{&quot;name&quot;:&quot;RecAgenziaCellPersonalePreferred&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:10},{&quot;name&quot;:&quot;RecAgenziaCellPersonale&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:11},{&quot;name&quot;:&quot;RecAgenziaFaxId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:12},{&quot;name&quot;:&quot;RecAgenziaFaxUsage&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:13},{&quot;name&quot;:&quot;RecAgenziaFaxPreferred&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:14},{&quot;name&quot;:&quot;RecAgenziaFax&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:15},{&quot;name&quot;:&quot;inserisci.userInfo.username&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:17},{&quot;name&quot;:&quot;inserisci.userInfo.userId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:18},{&quot;name&quot;:&quot;inserisci.userInfo.compagnia&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:19}]},&quot;state0element1block_element9_0&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;AnagDetails_RecapitiDelete&quot;,&quot;vlocityAsync&quot;:false},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[]},&quot;state0element1block_element9block_element0_0&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;AnagDetails_RecapitiDelete&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;recordId&quot;:&quot;{recordId}&quot;,&quot;userId&quot;:&quot;{User.userId}&quot;,&quot;Recapito&quot;:&quot;{RecAgenziaPEC}&quot;,&quot;Preferred&quot;:&quot;{RecAgenziaPECPreferred}&quot;,&quot;Usage&quot;:&quot;{RecAgenziaPECUsage}&quot;,&quot;idContatto&quot;:&quot;{RecAgenziaPECId}&quot;,&quot;tipoContatto&quot;:&quot;PEC&quot;,&quot;username&quot;:&quot;{inserisci.userInfo.username}&quot;,&quot;userIdReqBody&quot;:&quot;{inserisci.userInfo.userId}&quot;,&quot;compagnia&quot;:&quot;{inserisci.userInfo.compagnia}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;recordId\&quot;:\&quot;{recordId}\&quot;,\&quot;User.userId\&quot;:\&quot;{User.userId}\&quot;,\&quot;RecAgenziaPECId\&quot;:\&quot;{RecAgenziaPECId}\&quot;,\&quot;RecAgenziaPECUsage\&quot;:\&quot;{RecAgenziaPECUsage}\&quot;,\&quot;RecAgenziaPECPreferred\&quot;:\&quot;{RecAgenziaPECPreferred}\&quot;,\&quot;RecAgenziaPEC\&quot;:\&quot;{RecAgenziaPEC}\&quot;,\&quot;inserisci.userInfo.username\&quot;:\&quot;{inserisci.userInfo.username}\&quot;,\&quot;inserisci.userInfo.userId\&quot;:\&quot;{inserisci.userInfo.userId}\&quot;,\&quot;inserisci.userInfo.compagnia\&quot;:\&quot;{inserisci.userInfo.compagnia}\&quot;}&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:1},{&quot;name&quot;:&quot;User.userId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:3},{&quot;name&quot;:&quot;RecAgenziaPECId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:8},{&quot;name&quot;:&quot;RecAgenziaPECUsage&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:9},{&quot;name&quot;:&quot;RecAgenziaPECPreferred&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:10},{&quot;name&quot;:&quot;RecAgenziaPEC&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:11},{&quot;name&quot;:&quot;inserisci.userInfo.username&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:17},{&quot;name&quot;:&quot;inserisci.userInfo.userId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:18},{&quot;name&quot;:&quot;inserisci.userInfo.compagnia&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:19}]},&quot;state0element1block_element11_0&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;AnagDetails_RecapitiDelete&quot;,&quot;vlocityAsync&quot;:false},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[]},&quot;state0element1block_element11block_element0_0&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;AnagDetails_RecapitiDelete&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;recordId&quot;:&quot;{recordId}&quot;,&quot;userId&quot;:&quot;{User.userId}&quot;,&quot;Recapito&quot;:&quot;{RecAgenziaTelefono}&quot;,&quot;Preferred&quot;:&quot;{RecAgenziaTelefonoPreferred}&quot;,&quot;Usage&quot;:&quot;{RecAgenziaTelefonoUsage}&quot;,&quot;idContatto&quot;:&quot;{RecAgenziaTelefonoId}&quot;,&quot;tipoContatto&quot;:&quot;TEL&quot;,&quot;username&quot;:&quot;{inserisci.userInfo.username}&quot;,&quot;userIdReqBody&quot;:&quot;{inserisci.userInfo.userId}&quot;,&quot;compagnia&quot;:&quot;{inserisci.userInfo.compagnia}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;recordId\&quot;:\&quot;{recordId}\&quot;,\&quot;User.userId\&quot;:\&quot;{User.userId}\&quot;,\&quot;RecAgenziaTelefonoId\&quot;:\&quot;{RecAgenziaTelefonoId}\&quot;,\&quot;RecAgenziaTelefonoUsage\&quot;:\&quot;{RecAgenziaTelefonoUsage}\&quot;,\&quot;RecAgenziaTelefonoPreferred\&quot;:\&quot;{RecAgenziaTelefonoPreferred}\&quot;,\&quot;RecAgenziaTelefono\&quot;:\&quot;{RecAgenziaTelefono}\&quot;,\&quot;inserisci.userInfo.username\&quot;:\&quot;{inserisci.userInfo.username}\&quot;,\&quot;inserisci.userInfo.userId\&quot;:\&quot;{inserisci.userInfo.userId}\&quot;,\&quot;inserisci.userInfo.compagnia\&quot;:\&quot;{inserisci.userInfo.compagnia}\&quot;}&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:1},{&quot;name&quot;:&quot;User.userId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:3},{&quot;name&quot;:&quot;RecAgenziaTelefonoId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:8},{&quot;name&quot;:&quot;RecAgenziaTelefonoUsage&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:9},{&quot;name&quot;:&quot;RecAgenziaTelefonoPreferred&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:10},{&quot;name&quot;:&quot;RecAgenziaTelefono&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:11},{&quot;name&quot;:&quot;inserisci.userInfo.username&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:17},{&quot;name&quot;:&quot;inserisci.userInfo.userId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:18},{&quot;name&quot;:&quot;inserisci.userInfo.compagnia&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:19}]},&quot;state0element1block_element13_0&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;AnagDetails_RecapitiDelete&quot;,&quot;vlocityAsync&quot;:false},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[]},&quot;state0element1block_element15_0&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;AnagDetails_RecapitiDelete&quot;,&quot;vlocityAsync&quot;:false},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[]},&quot;state0element1block_element15block_element0_0&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;AnagDetails_RecapitiDelete&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;userId&quot;:&quot;{User.userId}&quot;,&quot;recordId&quot;:&quot;{recordId}&quot;,&quot;RecapitoReferente&quot;:&quot;{RecAgenziaReferente}&quot;,&quot;Recapito&quot;:&quot;{RecAgenziaTelefonoReferente}&quot;,&quot;Preferred&quot;:&quot;{RecAgenziaTelefonoReferentePreferred}&quot;,&quot;Usage&quot;:&quot;{RecAgenziaTelefonoReferenteUsage}&quot;,&quot;idContatto&quot;:&quot;{RecAgenziaTelefonoReferenteId}&quot;,&quot;tipoContatto&quot;:&quot;TELREF&quot;,&quot;username&quot;:&quot;{inserisci.userInfo.username}&quot;,&quot;userIdReqBody&quot;:&quot;{inserisci.userInfo.userId}&quot;,&quot;compagnia&quot;:&quot;{inserisci.userInfo.compagnia}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;User.userId\&quot;:\&quot;{User.userId}\&quot;,\&quot;recordId\&quot;:\&quot;{recordId}\&quot;,\&quot;RecAgenziaReferente\&quot;:\&quot;{RecAgenziaReferente}\&quot;,\&quot;RecAgenziaTelefonoReferenteId\&quot;:\&quot;{RecAgenziaTelefonoReferenteId}\&quot;,\&quot;RecAgenziaTelefonoReferenteUsage\&quot;:\&quot;{RecAgenziaTelefonoReferenteUsage}\&quot;,\&quot;RecAgenziaTelefonoReferentePreferred\&quot;:\&quot;{RecAgenziaTelefonoReferentePreferred}\&quot;,\&quot;RecAgenziaTelefonoReferente\&quot;:\&quot;{RecAgenziaTelefonoReferente}\&quot;,\&quot;inserisci.userInfo.username\&quot;:\&quot;{inserisci.userInfo.username}\&quot;,\&quot;inserisci.userInfo.userId\&quot;:\&quot;{inserisci.userInfo.userId}\&quot;,\&quot;inserisci.userInfo.compagnia\&quot;:\&quot;{inserisci.userInfo.compagnia}\&quot;}&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;User.userId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:1},{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:3},{&quot;name&quot;:&quot;RecAgenziaReferente&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:5},{&quot;name&quot;:&quot;RecAgenziaTelefonoReferenteId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:10},{&quot;name&quot;:&quot;RecAgenziaTelefonoReferenteUsage&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:11},{&quot;name&quot;:&quot;RecAgenziaTelefonoReferentePreferred&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:12},{&quot;name&quot;:&quot;RecAgenziaTelefonoReferente&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:13},{&quot;name&quot;:&quot;inserisci.userInfo.username&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:19},{&quot;name&quot;:&quot;inserisci.userInfo.userId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:20},{&quot;name&quot;:&quot;inserisci.userInfo.compagnia&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:21}]},&quot;state0element2block_element1block_element0_0&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;AnagDetails_RecapitiUpdate&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;RecAgenziaCellPersonale&quot;:&quot;{RecAgenziaCellPersonale}&quot;,&quot;RecAgenziaCellPersonalePreferred&quot;:&quot;{RecAgenziaCellPersonalePreferred}&quot;,&quot;RecAgenziaCellPersonaleUsage&quot;:&quot;{RecAgenziaCellPersonaleUsage}&quot;,&quot;RecAgenziaCellPersonaleId&quot;:&quot;{RecAgenziaCellPersonaleId}&quot;,&quot;RecAgenziaEmailPers&quot;:&quot;{RecAgenziaEmailPers}&quot;,&quot;RecAgenziaEmailPersPreferred&quot;:&quot;{RecAgenziaEmailPersPreferred}&quot;,&quot;RecAgenziaEmailPersUsage&quot;:&quot;{RecAgenziaEmailPersUsage}&quot;,&quot;RecAgenziaEmailPersId&quot;:&quot;{RecAgenziaEmailPersId}&quot;,&quot;RecAgenziaPEC&quot;:&quot;{RecAgenziaPEC}&quot;,&quot;RecAgenziaPECPreferred&quot;:&quot;{RecAgenziaPECPreferred}&quot;,&quot;RecAgenziaPECUsage&quot;:&quot;{RecAgenziaPECUsage}&quot;,&quot;RecAgenziaPECId&quot;:&quot;{RecAgenziaPECId}&quot;,&quot;RecAgenziaFax&quot;:&quot;{RecAgenziaFax}&quot;,&quot;RecAgenziaFaxPreferred&quot;:&quot;{RecAgenziaFaxPreferred}&quot;,&quot;RecAgenziaFaxUsage&quot;:&quot;{RecAgenziaFaxUsage}&quot;,&quot;RecAgenziaFaxId&quot;:&quot;{RecAgenziaFaxId}&quot;,&quot;RecAgenziaTelefono&quot;:&quot;{RecAgenziaTelefono}&quot;,&quot;RecAgenziaTelefonoPreferred&quot;:&quot;{RecAgenziaTelefonoPreferred}&quot;,&quot;RecAgenziaTelefonoUsage&quot;:&quot;{RecAgenziaTelefonoUsage}&quot;,&quot;RecAgenziaTelefonoId&quot;:&quot;{RecAgenziaTelefonoId}&quot;,&quot;RecAgenziaTelefonoReferente&quot;:&quot;{RecAgenziaTelefonoReferente}&quot;,&quot;RecAgenziaTelefonoReferentePreferred&quot;:&quot;{RecAgenziaTelefonoReferentePreferred}&quot;,&quot;RecAgenziaTelefonoReferenteUsage&quot;:&quot;{RecAgenziaTelefonoReferenteUsage}&quot;,&quot;RecAgenziaTelefonoReferenteId&quot;:&quot;{RecAgenziaTelefonoReferenteId}&quot;,&quot;RecAgenziaReferente&quot;:&quot;{RecAgenziaReferente}&quot;,&quot;recordId&quot;:&quot;{recordId}&quot;,&quot;userId&quot;:&quot;{User.userId}&quot;,&quot;username&quot;:&quot;{inserisci.userInfo.username}&quot;,&quot;userIdReqBody&quot;:&quot;{inserisci.userInfo.userId}&quot;,&quot;compagnia&quot;:&quot;{inserisci.userInfo.compagnia}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;RecAgenziaCellulare\&quot;:\&quot;{RecAgenziaCellulare}\&quot;,\&quot;RecAgenziaCellularePreferred\&quot;:\&quot;{RecAgenziaCellularePreferred}\&quot;,\&quot;RecAgenziaCellulareUsage\&quot;:\&quot;{RecAgenziaCellulareUsage}\&quot;,\&quot;RecAgenziaCellulareId\&quot;:\&quot;{RecAgenziaCellulareId}\&quot;,\&quot;RecAgenziaEmail\&quot;:\&quot;{RecAgenziaEmail}\&quot;,\&quot;RecAgenziaEmailPreferred\&quot;:\&quot;{RecAgenziaEmailPreferred}\&quot;,\&quot;RecAgenziaEmailUsage\&quot;:\&quot;{RecAgenziaEmailUsage}\&quot;,\&quot;RecAgenziaEmailId\&quot;:\&quot;{RecAgenziaEmailId}\&quot;,\&quot;RecAgenziaPEC\&quot;:\&quot;{RecAgenziaPEC}\&quot;,\&quot;RecAgenziaPECPreferred\&quot;:\&quot;{RecAgenziaPECPreferred}\&quot;,\&quot;RecAgenziaPECUsage\&quot;:\&quot;{RecAgenziaPECUsage}\&quot;,\&quot;RecAgenziaPECId\&quot;:\&quot;{RecAgenziaPECId}\&quot;,\&quot;RecAgenziaFax\&quot;:\&quot;{RecAgenziaFax}\&quot;,\&quot;RecAgenziaFaxPreferred\&quot;:\&quot;{RecAgenziaFaxPreferred}\&quot;,\&quot;RecAgenziaFaxUsage\&quot;:\&quot;{RecAgenziaFaxUsage}\&quot;,\&quot;RecAgenziaFaxId\&quot;:\&quot;{RecAgenziaFaxId}\&quot;,\&quot;RecAgenziaTelefono\&quot;:\&quot;{RecAgenziaTelefono}\&quot;,\&quot;RecAgenziaTelefonoPreferred\&quot;:\&quot;{RecAgenziaTelefonoPreferred}\&quot;,\&quot;RecAgenziaTelefonoUsage\&quot;:\&quot;{RecAgenziaTelefonoUsage}\&quot;,\&quot;RecAgenziaTelefonoId\&quot;:\&quot;{RecAgenziaTelefonoId}\&quot;,\&quot;RecAgenziaTelefonoReferente\&quot;:\&quot;{RecAgenziaTelefonoReferente}\&quot;,\&quot;RecAgenziaTelefonoReferentePreferred\&quot;:\&quot;{RecAgenziaTelefonoReferentePreferred}\&quot;,\&quot;RecAgenziaTelefonoReferenteUsage\&quot;:\&quot;{RecAgenziaTelefonoReferenteUsage}\&quot;,\&quot;RecAgenziaTelefonoReferenteId\&quot;:\&quot;{RecAgenziaTelefonoReferenteId}\&quot;,\&quot;RecAgenziaReferente\&quot;:\&quot;{RecAgenziaReferente}\&quot;,\&quot;RecAgenziaCellPersonale\&quot;:\&quot;{RecAgenziaCellPersonale}\&quot;,\&quot;RecAgenziaCellPersonalePreferred\&quot;:\&quot;{RecAgenziaCellPersonalePreferred}\&quot;,\&quot;RecAgenziaCellPersonaleUsage\&quot;:\&quot;{RecAgenziaCellPersonaleUsage}\&quot;,\&quot;RecAgenziaCellPersonaleId\&quot;:\&quot;{RecAgenziaCellPersonaleId}\&quot;,\&quot;RecAgenziaEmailPers\&quot;:\&quot;{RecAgenziaEmailPers}\&quot;,\&quot;RecAgenziaEmailPersPreferred\&quot;:\&quot;{RecAgenziaEmailPersPreferred}\&quot;,\&quot;RecAgenziaEmailPersUsage\&quot;:\&quot;{RecAgenziaEmailPersUsage}\&quot;,\&quot;RecAgenziaEmailPersId\&quot;:\&quot;{RecAgenziaEmailPersId}\&quot;,\&quot;recordId\&quot;:\&quot;{recordId}\&quot;,\&quot;User.userId\&quot;:\&quot;{User.userId}\&quot;,\&quot;recapiti.inserisci.userInfo.compagnia\&quot;:\&quot;{recapiti.inserisci.userInfo.compagnia}\&quot;,\&quot;recapiti.inserisci.userInfo.username\&quot;:\&quot;{recapiti.inserisci.userInfo.username}\&quot;,\&quot;recapiti.inserisci.userInfo.userId\&quot;:\&quot;{recapiti.inserisci.userInfo.userId}\&quot;,\&quot;inserisci.userInfo.username\&quot;:\&quot;{inserisci.userInfo.username}\&quot;,\&quot;inserisci.userInfo.userId\&quot;:\&quot;{inserisci.userInfo.userId}\&quot;,\&quot;inserisci.userInfo.compagnia\&quot;:\&quot;{inserisci.userInfo.compagnia}\&quot;}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;RecAgenziaCellulare&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:2},{&quot;name&quot;:&quot;RecAgenziaCellularePreferred&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:4},{&quot;name&quot;:&quot;RecAgenziaCellulareUsage&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:6},{&quot;name&quot;:&quot;RecAgenziaCellulareId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:8},{&quot;name&quot;:&quot;RecAgenziaEmail&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:10},{&quot;name&quot;:&quot;RecAgenziaEmailPreferred&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:12},{&quot;name&quot;:&quot;RecAgenziaEmailUsage&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:14},{&quot;name&quot;:&quot;RecAgenziaEmailId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:16},{&quot;name&quot;:&quot;RecAgenziaPEC&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:18},{&quot;name&quot;:&quot;RecAgenziaPECPreferred&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:20},{&quot;name&quot;:&quot;RecAgenziaPECUsage&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:22},{&quot;name&quot;:&quot;RecAgenziaPECId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:24},{&quot;name&quot;:&quot;RecAgenziaFax&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:26},{&quot;name&quot;:&quot;RecAgenziaFaxPreferred&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:28},{&quot;name&quot;:&quot;RecAgenziaFaxUsage&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:30},{&quot;name&quot;:&quot;RecAgenziaFaxId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:32},{&quot;name&quot;:&quot;RecAgenziaTelefono&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:34},{&quot;name&quot;:&quot;RecAgenziaTelefonoPreferred&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:36},{&quot;name&quot;:&quot;RecAgenziaTelefonoUsage&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:38},{&quot;name&quot;:&quot;RecAgenziaTelefonoId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:40},{&quot;name&quot;:&quot;RecAgenziaTelefonoReferente&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:42},{&quot;name&quot;:&quot;RecAgenziaTelefonoReferentePreferred&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:44},{&quot;name&quot;:&quot;RecAgenziaTelefonoReferenteUsage&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:46},{&quot;name&quot;:&quot;RecAgenziaTelefonoReferenteId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:48},{&quot;name&quot;:&quot;RecAgenziaReferente&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:50},{&quot;name&quot;:&quot;RecAgenziaCellPersonale&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:56},{&quot;name&quot;:&quot;RecAgenziaCellPersonalePreferred&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:57},{&quot;name&quot;:&quot;RecAgenziaCellPersonaleUsage&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:58},{&quot;name&quot;:&quot;RecAgenziaCellPersonaleId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:59},{&quot;name&quot;:&quot;RecAgenziaEmailPers&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:60},{&quot;name&quot;:&quot;RecAgenziaEmailPersPreferred&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:61},{&quot;name&quot;:&quot;RecAgenziaEmailPersUsage&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:62},{&quot;name&quot;:&quot;RecAgenziaEmailPersId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:63},{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;a1i9X0000050jblQAA&quot;,&quot;id&quot;:81},{&quot;name&quot;:&quot;User.userId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:82},{&quot;name&quot;:&quot;recapiti.inserisci.userInfo.compagnia&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:57},{&quot;name&quot;:&quot;recapiti.inserisci.userInfo.username&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:58},{&quot;name&quot;:&quot;recapiti.inserisci.userInfo.userId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:59},{&quot;name&quot;:&quot;inserisci.userInfo.username&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:120},{&quot;name&quot;:&quot;inserisci.userInfo.userId&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:121},{&quot;name&quot;:&quot;inserisci.userInfo.compagnia&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:122}]}}</dataSourceConfig>
    <description>Simone Di Biagio: Inibizione propagazione</description>
    <isActive>false</isActive>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>DA_Recapiti_Modifica</name>
    <omniUiCardType>Child</omniUiCardType>
    <propertySetConfig>{&quot;states&quot;:[{&quot;fields&quot;:[],&quot;conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;definedActions&quot;:{&quot;actions&quot;:[]},&quot;name&quot;:&quot;Active&quot;,&quot;isSmartAction&quot;:false,&quot;smartAction&quot;:{},&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;none&quot;}],&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;,&quot;class&quot;:&quot;slds-card slds-p-around_x-small slds-m-bottom_x-small&quot;},&quot;components&quot;:{&quot;layer-0&quot;:{&quot;children&quot;:[{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-border_bottom slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_0_0_outputField_0_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-align_center%22%3E%3Cspan%20style=%22font-size:%2014pt;%22%3EModifica%20recapiti%3C/span%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;,&quot;elementLabel&quot;:&quot;Block-0-Text-0&quot;}],&quot;elementLabel&quot;:&quot;Block-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-border_bottom slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_bottom&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;     border-bottom: #cccccc 2px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:12},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:12},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;recapiti di agenzia&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#F3F3F3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#F3F3F3;      \n         &quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_element_block_1_0_block_0_0_outputField_0_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%3Cspan%20style=%22font-size:%2010pt;%22%3E%3Cstrong%3ERecapiti%20di%20agenzia%3C/strong%3E%3C/span%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_0_0&quot;,&quot;elementLabel&quot;:&quot;Block-0-Text-0&quot;}],&quot;elementLabel&quot;:&quot;Block-1-Block-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#F3F3F3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#F3F3F3;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_1_0_block_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;success&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;false&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_element_block_1_0_block_1_0_outputField_0_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-align_center%22%3E%3Cspan%20style=%22color:%20#e03e2d;%22%3E%7BerrorMessage%7D%3C/span%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_1_0&quot;,&quot;elementLabel&quot;:&quot;Block-1-Block-1-Text-0&quot;}],&quot;elementLabel&quot;:&quot;Block-1-Block-1&quot;,&quot;key&quot;:&quot;element_element_block_1_0_block_1_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;5&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;ShowCellulare&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]},&quot;data-preloadConditionalElement&quot;:false},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;5&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_5-of-12 &quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Cellulare&quot;,&quot;required&quot;:false,&quot;fieldBinding&quot;:&quot;{RecAgenziaCellPersonale}&quot;,&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1741091864605-uegd6qj7l&quot;,&quot;label&quot;:&quot;ResetError&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1741091901488&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;success&quot;,&quot;fieldValue&quot;:&quot;true&quot;}]},&quot;actionIndex&quot;:0,&quot;isTrackingDisabled&quot;:true}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1741091864605-uegd6qj7l&quot;,&quot;label&quot;:&quot;ResetError&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1741091901488&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;success&quot;,&quot;fieldValue&quot;:&quot;true&quot;}]},&quot;actionIndex&quot;:0,&quot;isTrackingDisabled&quot;:true}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-1-Text-1&quot;,&quot;key&quot;:&quot;element_element_element_block_1_0_block_2_0_baseInputElement_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_2_0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]}],&quot;elementLabel&quot;:&quot;Block-1-Block-2&quot;,&quot;key&quot;:&quot;element_element_block_1_0_block_2_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onclick&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1745578596420-qu5r0zwar&quot;,&quot;label&quot;:&quot;Delete Cell&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1747149713394&quot;,&quot;type&quot;:&quot;DataAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;message&quot;:&quot;{\&quot;type\&quot;:\&quot;IntegrationProcedures\&quot;,\&quot;value\&quot;:{\&quot;dsDelay\&quot;:\&quot;\&quot;,\&quot;ipMethod\&quot;:\&quot;AnagDetails_RecapitiDelete\&quot;,\&quot;vlocityAsync\&quot;:false,\&quot;inputMap\&quot;:{\&quot;RecAgenziaCellPersonale\&quot;:\&quot;{RecAgenziaCellPersonale}\&quot;,\&quot;RecAgenziaCellPersonalePreferred\&quot;:\&quot;{RecAgenziaCellPersonalePreferred}\&quot;,\&quot;RecAgenziaCellPersonaleUsage\&quot;:\&quot;{RecAgenziaCellPersonaleUsage}\&quot;,\&quot;RecAgenziaCellPersonaleId\&quot;:\&quot;{RecAgenziaCellPersonaleId}\&quot;,\&quot;userId\&quot;:\&quot;{User.userId}\&quot;},\&quot;jsonMap\&quot;:\&quot;{\\\&quot;RecAgenziaCellPersonale\\\&quot;:\\\&quot;{RecAgenziaCellPersonale}\\\&quot;,\\\&quot;RecAgenziaCellPersonalePreferred\\\&quot;:\\\&quot;{RecAgenziaCellPersonalePreferred}\\\&quot;,\\\&quot;RecAgenziaCellPersonaleUsage\\\&quot;:\\\&quot;{RecAgenziaCellPersonaleUsage}\\\&quot;,\\\&quot;RecAgenziaCellPersonaleId\\\&quot;:\\\&quot;{RecAgenziaCellPersonaleId}\\\&quot;,\\\&quot;User.userId\\\&quot;:\\\&quot;{User.userId}\\\&quot;}\&quot;},\&quot;orderBy\&quot;:{\&quot;name\&quot;:\&quot;\&quot;,\&quot;isReverse\&quot;:\&quot;\&quot;},\&quot;contextVariables\&quot;:[{\&quot;name\&quot;:\&quot;RecAgenziaCellPersonale\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:1},{\&quot;name\&quot;:\&quot;RecAgenziaCellPersonalePreferred\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:3},{\&quot;name\&quot;:\&quot;RecAgenziaCellPersonaleUsage\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:5},{\&quot;name\&quot;:\&quot;RecAgenziaCellPersonaleId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:7},{\&quot;name\&quot;:\&quot;User.userId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:9}]}&quot;},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;},&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-3&quot;,&quot;field&quot;:&quot;ShowCellulare&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-text-align_center slds-align_absolute-center slds-p-around_x-small slds-m-top_large slds-align_absolute-center&quot;,&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;margin&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;large&quot;,&quot;label&quot;:&quot;top:large&quot;}],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;slds-align_absolute-center&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;slds-align_absolute-center&quot;,&quot;style&quot;:&quot;      \n         slds-align_absolute-center&quot;,&quot;customClass&quot;:&quot;slds-align_absolute-center&quot;,&quot;height&quot;:&quot;&quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Action&quot;,&quot;element&quot;:&quot;action&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:12},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot; &quot;,&quot;iconName&quot;:&quot;utility:delete&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;stateObj&quot;:&quot;{record}&quot;,&quot;actionList&quot;:[{&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1753696037770&quot;,&quot;type&quot;:&quot;DataAction&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;message&quot;:&quot;{\&quot;type\&quot;:\&quot;IntegrationProcedures\&quot;,\&quot;value\&quot;:{\&quot;dsDelay\&quot;:\&quot;\&quot;,\&quot;ipMethod\&quot;:\&quot;AnagDetails_RecapitiDelete\&quot;,\&quot;vlocityAsync\&quot;:false,\&quot;inputMap\&quot;:{\&quot;recordId\&quot;:\&quot;{recordId}\&quot;,\&quot;userId\&quot;:\&quot;{User.userId}\&quot;,\&quot;Recapito\&quot;:\&quot;{RecAgenziaCellPersonale}\&quot;,\&quot;Preferred\&quot;:\&quot;{RecAgenziaCellPersonalePreferred}\&quot;,\&quot;Usage\&quot;:\&quot;{RecAgenziaCellPersonaleUsage}\&quot;,\&quot;idContatto\&quot;:\&quot;{RecAgenziaCellPersonaleId}\&quot;,\&quot;tipoContatto\&quot;:\&quot;CELL\&quot;,\&quot;username\&quot;:\&quot;{inserisci.userInfo.username}\&quot;,\&quot;userIdReqBody\&quot;:\&quot;{inserisci.userInfo.userId}\&quot;,\&quot;compagnia\&quot;:\&quot;{inserisci.userInfo.compagnia}\&quot;},\&quot;jsonMap\&quot;:\&quot;{\\\&quot;recordId\\\&quot;:\\\&quot;{recordId}\\\&quot;,\\\&quot;userId\\\&quot;:\\\&quot;{userId}\\\&quot;,\\\&quot;RecAgenziaCellPersonale\\\&quot;:\\\&quot;{RecAgenziaCellPersonale}\\\&quot;,\\\&quot;RecAgenziaCellPersonalePreferred\\\&quot;:\\\&quot;{RecAgenziaCellPersonalePreferred}\\\&quot;,\\\&quot;RecAgenziaCellPersonaleUsage\\\&quot;:\\\&quot;{RecAgenziaCellPersonaleUsage}\\\&quot;,\\\&quot;RecAgenziaCellPersonaleId\\\&quot;:\\\&quot;{RecAgenziaCellPersonaleId}\\\&quot;,\\\&quot;User.userId\\\&quot;:\\\&quot;{User.userId}\\\&quot;,\\\&quot;inserisci.userInfo.username\\\&quot;:\\\&quot;{inserisci.userInfo.username}\\\&quot;,\\\&quot;inserisci.userInfo.userId\\\&quot;:\\\&quot;{inserisci.userInfo.userId}\\\&quot;,\\\&quot;inserisci.userInfo.compagnia\\\&quot;:\\\&quot;{inserisci.userInfo.compagnia}\\\&quot;}\&quot;},\&quot;orderBy\&quot;:{\&quot;name\&quot;:\&quot;\&quot;,\&quot;isReverse\&quot;:\&quot;\&quot;},\&quot;contextVariables\&quot;:[{\&quot;name\&quot;:\&quot;recordId\&quot;,\&quot;val\&quot;:\&quot;{recordId}\&quot;,\&quot;id\&quot;:1},{\&quot;name\&quot;:\&quot;userId\&quot;,\&quot;val\&quot;:\&quot;{User.userId}\&quot;,\&quot;id\&quot;:3},{\&quot;name\&quot;:\&quot;RecAgenziaCellPersonale\&quot;,\&quot;val\&quot;:\&quot;{RecAgenziaCellPersonale}\&quot;,\&quot;id\&quot;:4},{\&quot;name\&quot;:\&quot;RecAgenziaCellPersonalePreferred\&quot;,\&quot;val\&quot;:\&quot;{RecAgenziaCellPersonalePreferred}\&quot;,\&quot;id\&quot;:4},{\&quot;name\&quot;:\&quot;RecAgenziaCellPersonaleUsage\&quot;,\&quot;val\&quot;:\&quot;{RecAgenziaCellPersonaleUsage}\&quot;,\&quot;id\&quot;:5},{\&quot;name\&quot;:\&quot;RecAgenziaCellPersonaleId\&quot;,\&quot;val\&quot;:\&quot;{RecAgenziaCellPersonaleId}\&quot;,\&quot;id\&quot;:6},{\&quot;name\&quot;:\&quot;User.userId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:4},{\&quot;name\&quot;:\&quot;inserisci.userInfo.username\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:17},{\&quot;name\&quot;:\&quot;inserisci.userInfo.userId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:18},{\&quot;name\&quot;:\&quot;inserisci.userInfo.compagnia\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:19}]}&quot;},&quot;key&quot;:&quot;1747215870882-p4lsfbjgq&quot;,&quot;label&quot;:&quot;Delete Cell&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;actionIndex&quot;:0,&quot;isTrackingDisabled&quot;:true},{&quot;key&quot;:&quot;1747216344557-bb6gm6s7a&quot;,&quot;label&quot;:&quot;Close&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1747216353636&quot;,&quot;type&quot;:&quot;Event&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;subType&quot;:&quot;PubSub&quot;,&quot;eventName&quot;:&quot;close_moda&quot;,&quot;message&quot;:&quot;close&quot;},&quot;actionIndex&quot;:1},{&quot;key&quot;:&quot;*************-7bipn9n0w&quot;,&quot;label&quot;:&quot;Refresh&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-*************&quot;,&quot;type&quot;:&quot;Custom&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Record&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Record&quot;:{&quot;targetName&quot;:&quot;AccountDetails__c&quot;,&quot;targetAction&quot;:&quot;view&quot;,&quot;targetId&quot;:&quot;{recordId}&quot;}},&quot;actionIndex&quot;:2}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;flyoutDetails&quot;:{}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:12},&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;Block-1-Block-3-Action-0&quot;,&quot;key&quot;:&quot;element_element_element_block_1_0_block_3_0_action_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_3_0&quot;}],&quot;elementLabel&quot;:&quot;Block-1-Block-3&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-text-align_center slds-align_absolute-center slds-p-around_x-small slds-m-top_large slds-align_absolute-center&quot;,&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;margin&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;large&quot;,&quot;label&quot;:&quot;top:large&quot;}],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;slds-align_absolute-center&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;slds-align_absolute-center&quot;,&quot;style&quot;:&quot;      \n         slds-align_absolute-center&quot;,&quot;customClass&quot;:&quot;slds-align_absolute-center&quot;,&quot;height&quot;:&quot;&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_1_0_block_3_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;},{&quot;key&quot;:&quot;element_element_block_1_0_block_4_0&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;5&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-5&quot;,&quot;field&quot;:&quot;ShowEmail&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]},&quot;data-preloadConditionalElement&quot;:false},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;5&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_5-of-12 &quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Email&quot;,&quot;fieldBinding&quot;:&quot;{RecAgenziaEmailPers}&quot;,&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1741091912286-kp36skwcf&quot;,&quot;label&quot;:&quot;ResetError&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1741091928702&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;success&quot;,&quot;fieldValue&quot;:&quot;true&quot;}]},&quot;isTrackingDisabled&quot;:true,&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1741091912286-kp36skwcf&quot;,&quot;label&quot;:&quot;ResetError&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1741091928702&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;success&quot;,&quot;fieldValue&quot;:&quot;true&quot;}]},&quot;isTrackingDisabled&quot;:true,&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-1-Text-1&quot;,&quot;key&quot;:&quot;element_element_element_block_1_0_block_3_0_baseInputElement_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_3_0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]}],&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;elementLabel&quot;:&quot;Block-1-Block-4&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onclick&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1745578738549-s59rj0gdc&quot;,&quot;label&quot;:&quot;Delete Mail&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1747147376001&quot;,&quot;type&quot;:&quot;DataAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;message&quot;:&quot;{\&quot;type\&quot;:\&quot;IntegrationProcedures\&quot;,\&quot;value\&quot;:{\&quot;dsDelay\&quot;:\&quot;\&quot;,\&quot;ipMethod\&quot;:\&quot;AnagDetails_RecapitiDelete\&quot;,\&quot;vlocityAsync\&quot;:false},\&quot;orderBy\&quot;:{\&quot;name\&quot;:\&quot;\&quot;,\&quot;isReverse\&quot;:\&quot;\&quot;},\&quot;contextVariables\&quot;:[]}&quot;},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;},&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-31&quot;,&quot;field&quot;:&quot;ShowEmail&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-text-align_center slds-p-around_x-small slds-m-top_large &quot;,&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;margin&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;large&quot;,&quot;label&quot;:&quot;top:large&quot;}],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Action&quot;,&quot;element&quot;:&quot;action&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:12},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot; &quot;,&quot;iconName&quot;:&quot;utility:delete&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;stateObj&quot;:&quot;{record}&quot;,&quot;actionList&quot;:[{&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1753696322231&quot;,&quot;type&quot;:&quot;DataAction&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;message&quot;:&quot;{\&quot;type\&quot;:\&quot;IntegrationProcedures\&quot;,\&quot;value\&quot;:{\&quot;dsDelay\&quot;:\&quot;\&quot;,\&quot;ipMethod\&quot;:\&quot;AnagDetails_RecapitiDelete\&quot;,\&quot;vlocityAsync\&quot;:false,\&quot;inputMap\&quot;:{\&quot;recordId\&quot;:\&quot;{recordId}\&quot;,\&quot;userId\&quot;:\&quot;{User.userId}\&quot;,\&quot;Recapito\&quot;:\&quot;{RecAgenziaEmailPers}\&quot;,\&quot;Preferred\&quot;:\&quot;{RecAgenziaEmailPersPreferred}\&quot;,\&quot;Usage\&quot;:\&quot;{RecAgenziaEmailPersUsage}\&quot;,\&quot;idContatto\&quot;:\&quot;{RecAgenziaEmailPersId}\&quot;,\&quot;tipoContatto\&quot;:\&quot;MAIL\&quot;,\&quot;username\&quot;:\&quot;{inserisci.userInfo.username}\&quot;,\&quot;userIdReqBody\&quot;:\&quot;{inserisci.userInfo.userId}\&quot;,\&quot;compagnia\&quot;:\&quot;{inserisci.userInfo.compagnia}\&quot;},\&quot;jsonMap\&quot;:\&quot;{\\\&quot;User.userId\\\&quot;:\\\&quot;{User.userId}\\\&quot;,\\\&quot;recordId\\\&quot;:\\\&quot;{recordId}\\\&quot;,\\\&quot;RecAgenziaEmailPers\\\&quot;:\\\&quot;{RecAgenziaEmailPers}\\\&quot;,\\\&quot;RecAgenziaEmailPersPreferred\\\&quot;:\\\&quot;{RecAgenziaEmailPersPreferred}\\\&quot;,\\\&quot;RecAgenziaEmailPersUsage\\\&quot;:\\\&quot;{RecAgenziaEmailPersUsage}\\\&quot;,\\\&quot;RecAgenziaEmailPersId\\\&quot;:\\\&quot;{RecAgenziaEmailPersId}\\\&quot;,\\\&quot;inserisci.userInfo.compagnia\\\&quot;:\\\&quot;{inserisci.userInfo.compagnia}\\\&quot;,\\\&quot;inserisci.userInfo.userId\\\&quot;:\\\&quot;{inserisci.userInfo.userId}\\\&quot;,\\\&quot;inserisci.userInfo.username\\\&quot;:\\\&quot;{inserisci.userInfo.username}\\\&quot;}\&quot;},\&quot;orderBy\&quot;:{\&quot;name\&quot;:\&quot;\&quot;,\&quot;isReverse\&quot;:\&quot;\&quot;},\&quot;contextVariables\&quot;:[{\&quot;name\&quot;:\&quot;User.userId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:2},{\&quot;name\&quot;:\&quot;recordId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:3},{\&quot;name\&quot;:\&quot;RecAgenziaEmailPers\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:8},{\&quot;name\&quot;:\&quot;RecAgenziaEmailPersPreferred\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:9},{\&quot;name\&quot;:\&quot;RecAgenziaEmailPersUsage\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:10},{\&quot;name\&quot;:\&quot;RecAgenziaEmailPersId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:11},{\&quot;name\&quot;:\&quot;inserisci.userInfo.compagnia\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:17},{\&quot;name\&quot;:\&quot;inserisci.userInfo.userId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:18},{\&quot;name\&quot;:\&quot;inserisci.userInfo.username\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:19}]}&quot;},&quot;key&quot;:&quot;1747216271909-y86psfsq9&quot;,&quot;label&quot;:&quot;Delete Email&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;actionIndex&quot;:0},{&quot;key&quot;:&quot;1747216735141-utzpqv3kg&quot;,&quot;label&quot;:&quot;Close&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1747216749064&quot;,&quot;type&quot;:&quot;Event&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;subType&quot;:&quot;PubSub&quot;,&quot;eventName&quot;:&quot;close_moda&quot;,&quot;message&quot;:&quot;close&quot;},&quot;actionIndex&quot;:1},{&quot;key&quot;:&quot;*************-m5h0mfzzw&quot;,&quot;label&quot;:&quot;Refresh&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-*************&quot;,&quot;type&quot;:&quot;Custom&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Record&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Record&quot;:{&quot;targetName&quot;:&quot;AccountDetails__c&quot;,&quot;targetId&quot;:&quot;{recordId}&quot;,&quot;targetAction&quot;:&quot;view&quot;}},&quot;actionIndex&quot;:2}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;flyoutDetails&quot;:{}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:12},&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;Block-1-Block-7-clone-0-Action-0-clone-0&quot;,&quot;key&quot;:&quot;element_element_element_block_1_0_block_5_0_action_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_5_0&quot;}],&quot;elementLabel&quot;:&quot;Block-1-Block-3-clone-0&quot;,&quot;key&quot;:&quot;element_element_block_1_0_block_5_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-text-align_center slds-p-around_x-small slds-m-top_large &quot;,&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;margin&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;large&quot;,&quot;label&quot;:&quot;top:large&quot;}],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;key&quot;:&quot;element_element_block_1_0_block_6_0&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;5&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-10&quot;,&quot;field&quot;:&quot;ShowFax&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]},&quot;data-preloadConditionalElement&quot;:false},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_5-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;5&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_element_block_1_0_block_4_0_baseInputElement_0_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Fax&quot;,&quot;fieldBinding&quot;:&quot;{RecAgenziaFax}&quot;,&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1741091941699-zmngjmgf4&quot;,&quot;label&quot;:&quot;ResetError&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1741091953615&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;success&quot;,&quot;fieldValue&quot;:&quot;true&quot;}]},&quot;actionIndex&quot;:0,&quot;isTrackingDisabled&quot;:true}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1741091941699-zmngjmgf4&quot;,&quot;label&quot;:&quot;ResetError&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1741091953615&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;success&quot;,&quot;fieldValue&quot;:&quot;true&quot;}]},&quot;actionIndex&quot;:0,&quot;isTrackingDisabled&quot;:true}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_4_0&quot;,&quot;elementLabel&quot;:&quot;Block-1-Block-3-Text-0&quot;}],&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;elementLabel&quot;:&quot;Block-1-Block-5&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_5-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;5&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onclick&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1745578658504-uip7wuzq4&quot;,&quot;label&quot;:&quot;Delete Fax&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1747147271592&quot;,&quot;type&quot;:&quot;DataAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;message&quot;:&quot;{\&quot;type\&quot;:\&quot;IntegrationProcedures\&quot;,\&quot;value\&quot;:{\&quot;dsDelay\&quot;:\&quot;\&quot;,\&quot;ipMethod\&quot;:\&quot;AnagDetails_RecapitiDelete\&quot;,\&quot;vlocityAsync\&quot;:false},\&quot;orderBy\&quot;:{\&quot;name\&quot;:\&quot;\&quot;,\&quot;isReverse\&quot;:\&quot;\&quot;},\&quot;contextVariables\&quot;:[]}&quot;},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;},&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-10&quot;,&quot;field&quot;:&quot;ShowFax&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-text-align_center slds-p-around_x-small slds-m-top_large &quot;,&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;margin&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;large&quot;,&quot;label&quot;:&quot;top:large&quot;}],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Action&quot;,&quot;element&quot;:&quot;action&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:12},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot; &quot;,&quot;iconName&quot;:&quot;utility:delete&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;stateObj&quot;:&quot;{record}&quot;,&quot;actionList&quot;:[{&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1753696103776&quot;,&quot;type&quot;:&quot;DataAction&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;message&quot;:&quot;{\&quot;type\&quot;:\&quot;IntegrationProcedures\&quot;,\&quot;value\&quot;:{\&quot;dsDelay\&quot;:\&quot;\&quot;,\&quot;ipMethod\&quot;:\&quot;AnagDetails_RecapitiDelete\&quot;,\&quot;vlocityAsync\&quot;:false,\&quot;inputMap\&quot;:{\&quot;userId\&quot;:\&quot;{User.userId}\&quot;,\&quot;recordId\&quot;:\&quot;{recordId}\&quot;,\&quot;Recapito\&quot;:\&quot;{RecAgenziaFax}\&quot;,\&quot;Preferred\&quot;:\&quot;{RecAgenziaFaxPreferred}\&quot;,\&quot;RecAgenziaFaxUsage\&quot;:\&quot;{RecAgenziaFaxUsage}\&quot;,\&quot;idContatto\&quot;:\&quot;{RecAgenziaFaxId}\&quot;,\&quot;tipoContatto\&quot;:\&quot;FAX\&quot;,\&quot;username\&quot;:\&quot;{inserisci.userInfo.username}\&quot;,\&quot;userIdReqBody\&quot;:\&quot;{inserisci.userInfo.userId}\&quot;,\&quot;compagnia\&quot;:\&quot;{inserisci.userInfo.compagnia}\&quot;},\&quot;jsonMap\&quot;:\&quot;{\\\&quot;User.userId\\\&quot;:\\\&quot;{User.userId}\\\&quot;,\\\&quot;recordId\\\&quot;:\\\&quot;{recordId}\\\&quot;,\\\&quot;RecAgenziaCellPersonaleId\\\&quot;:\\\&quot;{RecAgenziaCellPersonaleId}\\\&quot;,\\\&quot;RecAgenziaCellPersonaleUsage\\\&quot;:\\\&quot;{RecAgenziaCellPersonaleUsage}\\\&quot;,\\\&quot;RecAgenziaCellPersonalePreferred\\\&quot;:\\\&quot;{RecAgenziaCellPersonalePreferred}\\\&quot;,\\\&quot;RecAgenziaCellPersonale\\\&quot;:\\\&quot;{RecAgenziaCellPersonale}\\\&quot;,\\\&quot;RecAgenziaFaxId\\\&quot;:\\\&quot;{RecAgenziaFaxId}\\\&quot;,\\\&quot;RecAgenziaFaxUsage\\\&quot;:\\\&quot;{RecAgenziaFaxUsage}\\\&quot;,\\\&quot;RecAgenziaFaxPreferred\\\&quot;:\\\&quot;{RecAgenziaFaxPreferred}\\\&quot;,\\\&quot;RecAgenziaFax\\\&quot;:\\\&quot;{RecAgenziaFax}\\\&quot;,\\\&quot;inserisci.userInfo.username\\\&quot;:\\\&quot;{inserisci.userInfo.username}\\\&quot;,\\\&quot;inserisci.userInfo.userId\\\&quot;:\\\&quot;{inserisci.userInfo.userId}\\\&quot;,\\\&quot;inserisci.userInfo.compagnia\\\&quot;:\\\&quot;{inserisci.userInfo.compagnia}\\\&quot;}\&quot;},\&quot;orderBy\&quot;:{\&quot;name\&quot;:\&quot;\&quot;,\&quot;isReverse\&quot;:\&quot;\&quot;},\&quot;contextVariables\&quot;:[{\&quot;name\&quot;:\&quot;User.userId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:1},{\&quot;name\&quot;:\&quot;recordId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:3},{\&quot;name\&quot;:\&quot;RecAgenziaCellPersonaleId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:8},{\&quot;name\&quot;:\&quot;RecAgenziaCellPersonaleUsage\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:9},{\&quot;name\&quot;:\&quot;RecAgenziaCellPersonalePreferred\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:10},{\&quot;name\&quot;:\&quot;RecAgenziaCellPersonale\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:11},{\&quot;name\&quot;:\&quot;RecAgenziaFaxId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:12},{\&quot;name\&quot;:\&quot;RecAgenziaFaxUsage\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:13},{\&quot;name\&quot;:\&quot;RecAgenziaFaxPreferred\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:14},{\&quot;name\&quot;:\&quot;RecAgenziaFax\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:15},{\&quot;name\&quot;:\&quot;inserisci.userInfo.username\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:17},{\&quot;name\&quot;:\&quot;inserisci.userInfo.userId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:18},{\&quot;name\&quot;:\&quot;inserisci.userInfo.compagnia\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:19}]}&quot;},&quot;key&quot;:&quot;1747215965620-8gfcc1swj&quot;,&quot;label&quot;:&quot;Delete Fax&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;actionIndex&quot;:0},{&quot;key&quot;:&quot;1747216448422-xdymlxfch&quot;,&quot;label&quot;:&quot;Close&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1747216467112&quot;,&quot;type&quot;:&quot;Event&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;subType&quot;:&quot;PubSub&quot;,&quot;eventName&quot;:&quot;close_moda&quot;,&quot;message&quot;:&quot;close&quot;},&quot;actionIndex&quot;:1},{&quot;key&quot;:&quot;*************-sjwmkaxrp&quot;,&quot;label&quot;:&quot;Refresh&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-*************&quot;,&quot;type&quot;:&quot;Custom&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Record&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Record&quot;:{&quot;targetName&quot;:&quot;AccountDetails__c&quot;,&quot;targetId&quot;:&quot;{recordId}&quot;,&quot;targetAction&quot;:&quot;view&quot;}},&quot;actionIndex&quot;:2}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;flyoutDetails&quot;:{}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:12},&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;Block-1-Block-3-Action-0-clone-0&quot;,&quot;key&quot;:&quot;element_element_element_block_1_0_block_7_0_action_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_7_0&quot;}],&quot;elementLabel&quot;:&quot;Block-1-Block-5-clone-0&quot;,&quot;key&quot;:&quot;element_element_block_1_0_block_7_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-text-align_center slds-p-around_x-small slds-m-top_large &quot;,&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;margin&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;large&quot;,&quot;label&quot;:&quot;top:large&quot;}],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;key&quot;:&quot;element_element_block_1_0_block_8_0&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;5&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-15&quot;,&quot;field&quot;:&quot;ShowPEC&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]},&quot;data-preloadConditionalElement&quot;:false},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;5&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_5-of-12 &quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_element_block_1_0_block_5_0_baseInputElement_0_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Posta certificata&quot;,&quot;fieldBinding&quot;:&quot;{RecAgenziaPEC}&quot;,&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1741091964820-l6oyz1mo2&quot;,&quot;label&quot;:&quot;ResetError&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1741091975489&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;success&quot;,&quot;fieldValue&quot;:&quot;true&quot;}]},&quot;actionIndex&quot;:0,&quot;isTrackingDisabled&quot;:true}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1741091964820-l6oyz1mo2&quot;,&quot;label&quot;:&quot;ResetError&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1741091975489&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;success&quot;,&quot;fieldValue&quot;:&quot;true&quot;}]},&quot;actionIndex&quot;:0,&quot;isTrackingDisabled&quot;:true}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_5_0&quot;,&quot;elementLabel&quot;:&quot;Block-1-Block-4-Text-0&quot;}],&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;elementLabel&quot;:&quot;Block-1-Block-6&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onclick&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1745578770340-hegz9fig5&quot;,&quot;label&quot;:&quot;Delete PEC&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1747147405568&quot;,&quot;type&quot;:&quot;DataAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;message&quot;:&quot;{\&quot;type\&quot;:\&quot;IntegrationProcedures\&quot;,\&quot;value\&quot;:{\&quot;dsDelay\&quot;:\&quot;\&quot;,\&quot;ipMethod\&quot;:\&quot;AnagDetails_RecapitiDelete\&quot;,\&quot;vlocityAsync\&quot;:false},\&quot;orderBy\&quot;:{\&quot;name\&quot;:\&quot;\&quot;,\&quot;isReverse\&quot;:\&quot;\&quot;},\&quot;contextVariables\&quot;:[]}&quot;},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;},&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-38&quot;,&quot;field&quot;:&quot;ShowPEC&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-text-align_center slds-p-around_x-small slds-m-top_large &quot;,&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;margin&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;large&quot;,&quot;label&quot;:&quot;top:large&quot;}],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Action&quot;,&quot;element&quot;:&quot;action&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:12},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot; &quot;,&quot;iconName&quot;:&quot;utility:delete&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;stateObj&quot;:&quot;{record}&quot;,&quot;actionList&quot;:[{&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1753696367353&quot;,&quot;type&quot;:&quot;DataAction&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;message&quot;:&quot;{\&quot;type\&quot;:\&quot;IntegrationProcedures\&quot;,\&quot;value\&quot;:{\&quot;dsDelay\&quot;:\&quot;\&quot;,\&quot;ipMethod\&quot;:\&quot;AnagDetails_RecapitiDelete\&quot;,\&quot;vlocityAsync\&quot;:false,\&quot;inputMap\&quot;:{\&quot;recordId\&quot;:\&quot;{recordId}\&quot;,\&quot;userId\&quot;:\&quot;{User.userId}\&quot;,\&quot;Recapito\&quot;:\&quot;{RecAgenziaPEC}\&quot;,\&quot;Preferred\&quot;:\&quot;{RecAgenziaPECPreferred}\&quot;,\&quot;Usage\&quot;:\&quot;{RecAgenziaPECUsage}\&quot;,\&quot;idContatto\&quot;:\&quot;{RecAgenziaPECId}\&quot;,\&quot;tipoContatto\&quot;:\&quot;PEC\&quot;,\&quot;username\&quot;:\&quot;{inserisci.userInfo.username}\&quot;,\&quot;userIdReqBody\&quot;:\&quot;{inserisci.userInfo.userId}\&quot;,\&quot;compagnia\&quot;:\&quot;{inserisci.userInfo.compagnia}\&quot;},\&quot;jsonMap\&quot;:\&quot;{\\\&quot;recordId\\\&quot;:\\\&quot;{recordId}\\\&quot;,\\\&quot;User.userId\\\&quot;:\\\&quot;{User.userId}\\\&quot;,\\\&quot;RecAgenziaPECId\\\&quot;:\\\&quot;{RecAgenziaPECId}\\\&quot;,\\\&quot;RecAgenziaPECUsage\\\&quot;:\\\&quot;{RecAgenziaPECUsage}\\\&quot;,\\\&quot;RecAgenziaPECPreferred\\\&quot;:\\\&quot;{RecAgenziaPECPreferred}\\\&quot;,\\\&quot;RecAgenziaPEC\\\&quot;:\\\&quot;{RecAgenziaPEC}\\\&quot;,\\\&quot;inserisci.userInfo.username\\\&quot;:\\\&quot;{inserisci.userInfo.username}\\\&quot;,\\\&quot;inserisci.userInfo.userId\\\&quot;:\\\&quot;{inserisci.userInfo.userId}\\\&quot;,\\\&quot;inserisci.userInfo.compagnia\\\&quot;:\\\&quot;{inserisci.userInfo.compagnia}\\\&quot;}\&quot;},\&quot;orderBy\&quot;:{\&quot;name\&quot;:\&quot;\&quot;,\&quot;isReverse\&quot;:\&quot;\&quot;},\&quot;contextVariables\&quot;:[{\&quot;name\&quot;:\&quot;recordId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:1},{\&quot;name\&quot;:\&quot;User.userId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:3},{\&quot;name\&quot;:\&quot;RecAgenziaPECId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:8},{\&quot;name\&quot;:\&quot;RecAgenziaPECUsage\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:9},{\&quot;name\&quot;:\&quot;RecAgenziaPECPreferred\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:10},{\&quot;name\&quot;:\&quot;RecAgenziaPEC\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:11},{\&quot;name\&quot;:\&quot;inserisci.userInfo.username\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:17},{\&quot;name\&quot;:\&quot;inserisci.userInfo.userId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:18},{\&quot;name\&quot;:\&quot;inserisci.userInfo.compagnia\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:19}]}&quot;},&quot;key&quot;:&quot;1747216227678-11yupe4yz&quot;,&quot;label&quot;:&quot;Delete PEC&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;actionIndex&quot;:0},{&quot;key&quot;:&quot;1747216691713-f3k755z09&quot;,&quot;label&quot;:&quot;Close&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1747216704392&quot;,&quot;type&quot;:&quot;Event&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;subType&quot;:&quot;PubSub&quot;,&quot;message&quot;:&quot;close&quot;,&quot;eventName&quot;:&quot;close_moda&quot;},&quot;actionIndex&quot;:1},{&quot;key&quot;:&quot;*************-flx6fftqg&quot;,&quot;label&quot;:&quot;Refresh&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-*************&quot;,&quot;type&quot;:&quot;Custom&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Record&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Record&quot;:{&quot;targetName&quot;:&quot;AccountDetails__c&quot;,&quot;targetId&quot;:&quot;{recordId}&quot;,&quot;targetAction&quot;:&quot;view&quot;}},&quot;actionIndex&quot;:2}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;flyoutDetails&quot;:{},&quot;ariaLabel&quot;:&quot; &quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:12},&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;Block-1-Block-11-clone-0-Action-0-clone-0&quot;,&quot;key&quot;:&quot;element_element_element_block_1_0_block_9_0_action_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_9_0&quot;}],&quot;elementLabel&quot;:&quot;Block-1-Block-7-clone-0&quot;,&quot;key&quot;:&quot;element_element_block_1_0_block_9_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-text-align_center slds-p-around_x-small slds-m-top_large &quot;,&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;margin&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;large&quot;,&quot;label&quot;:&quot;top:large&quot;}],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;key&quot;:&quot;element_element_block_1_0_block_10_0&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;5&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-20&quot;,&quot;field&quot;:&quot;ShowTelefono&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]},&quot;data-preloadConditionalElement&quot;:false},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_5-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;5&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_element_block_1_0_block_6_0_baseInputElement_0_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Telefono&quot;,&quot;fieldBinding&quot;:&quot;{RecAgenziaTelefono}&quot;,&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1741091987034-0ahzuf5w4&quot;,&quot;label&quot;:&quot;ResetError&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1741092000816&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;success&quot;,&quot;fieldValue&quot;:&quot;true&quot;}]},&quot;actionIndex&quot;:0,&quot;isTrackingDisabled&quot;:true}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1741091987034-0ahzuf5w4&quot;,&quot;label&quot;:&quot;ResetError&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1741092000816&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;success&quot;,&quot;fieldValue&quot;:&quot;true&quot;}]},&quot;actionIndex&quot;:0,&quot;isTrackingDisabled&quot;:true}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_6_0&quot;,&quot;elementLabel&quot;:&quot;Block-1-Block-5-Text-0&quot;}],&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;elementLabel&quot;:&quot;Block-1-Block-7&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_5-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;5&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onclick&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1745578680189-vkfgxbfh0&quot;,&quot;label&quot;:&quot;Delete  Tel&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1747147306973&quot;,&quot;type&quot;:&quot;DataAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;message&quot;:&quot;{\&quot;type\&quot;:\&quot;IntegrationProcedures\&quot;,\&quot;value\&quot;:{\&quot;dsDelay\&quot;:\&quot;\&quot;,\&quot;ipMethod\&quot;:\&quot;AnagDetails_RecapitiDelete\&quot;,\&quot;vlocityAsync\&quot;:false},\&quot;orderBy\&quot;:{\&quot;name\&quot;:\&quot;\&quot;,\&quot;isReverse\&quot;:\&quot;\&quot;},\&quot;contextVariables\&quot;:[]}&quot;},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;},&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-17&quot;,&quot;field&quot;:&quot;ShowTelefono&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-text-align_center slds-p-around_x-small slds-m-top_large &quot;,&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;margin&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;large&quot;,&quot;label&quot;:&quot;top:large&quot;}],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Action&quot;,&quot;element&quot;:&quot;action&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:12},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot; &quot;,&quot;iconName&quot;:&quot;utility:delete&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;stateObj&quot;:&quot;{record}&quot;,&quot;actionList&quot;:[{&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1753696281084&quot;,&quot;type&quot;:&quot;DataAction&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;message&quot;:&quot;{\&quot;type\&quot;:\&quot;IntegrationProcedures\&quot;,\&quot;value\&quot;:{\&quot;dsDelay\&quot;:\&quot;\&quot;,\&quot;ipMethod\&quot;:\&quot;AnagDetails_RecapitiDelete\&quot;,\&quot;vlocityAsync\&quot;:false,\&quot;inputMap\&quot;:{\&quot;recordId\&quot;:\&quot;{recordId}\&quot;,\&quot;userId\&quot;:\&quot;{User.userId}\&quot;,\&quot;Recapito\&quot;:\&quot;{RecAgenziaTelefono}\&quot;,\&quot;Preferred\&quot;:\&quot;{RecAgenziaTelefonoPreferred}\&quot;,\&quot;Usage\&quot;:\&quot;{RecAgenziaTelefonoUsage}\&quot;,\&quot;idContatto\&quot;:\&quot;{RecAgenziaTelefonoId}\&quot;,\&quot;tipoContatto\&quot;:\&quot;TEL\&quot;,\&quot;username\&quot;:\&quot;{inserisci.userInfo.username}\&quot;,\&quot;userIdReqBody\&quot;:\&quot;{inserisci.userInfo.userId}\&quot;,\&quot;compagnia\&quot;:\&quot;{inserisci.userInfo.compagnia}\&quot;},\&quot;jsonMap\&quot;:\&quot;{\\\&quot;recordId\\\&quot;:\\\&quot;{recordId}\\\&quot;,\\\&quot;User.userId\\\&quot;:\\\&quot;{User.userId}\\\&quot;,\\\&quot;RecAgenziaTelefonoId\\\&quot;:\\\&quot;{RecAgenziaTelefonoId}\\\&quot;,\\\&quot;RecAgenziaTelefonoUsage\\\&quot;:\\\&quot;{RecAgenziaTelefonoUsage}\\\&quot;,\\\&quot;RecAgenziaTelefonoPreferred\\\&quot;:\\\&quot;{RecAgenziaTelefonoPreferred}\\\&quot;,\\\&quot;RecAgenziaTelefono\\\&quot;:\\\&quot;{RecAgenziaTelefono}\\\&quot;,\\\&quot;inserisci.userInfo.username\\\&quot;:\\\&quot;{inserisci.userInfo.username}\\\&quot;,\\\&quot;inserisci.userInfo.userId\\\&quot;:\\\&quot;{inserisci.userInfo.userId}\\\&quot;,\\\&quot;inserisci.userInfo.compagnia\\\&quot;:\\\&quot;{inserisci.userInfo.compagnia}\\\&quot;}\&quot;},\&quot;orderBy\&quot;:{\&quot;name\&quot;:\&quot;\&quot;,\&quot;isReverse\&quot;:\&quot;\&quot;},\&quot;contextVariables\&quot;:[{\&quot;name\&quot;:\&quot;recordId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:1},{\&quot;name\&quot;:\&quot;User.userId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:3},{\&quot;name\&quot;:\&quot;RecAgenziaTelefonoId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:8},{\&quot;name\&quot;:\&quot;RecAgenziaTelefonoUsage\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:9},{\&quot;name\&quot;:\&quot;RecAgenziaTelefonoPreferred\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:10},{\&quot;name\&quot;:\&quot;RecAgenziaTelefono\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:11},{\&quot;name\&quot;:\&quot;inserisci.userInfo.username\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:17},{\&quot;name\&quot;:\&quot;inserisci.userInfo.userId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:18},{\&quot;name\&quot;:\&quot;inserisci.userInfo.compagnia\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:19}]}&quot;},&quot;key&quot;:&quot;1747216009678-pr8q2gpn0&quot;,&quot;label&quot;:&quot;Delete Tel&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:true,&quot;actionIndex&quot;:0},{&quot;key&quot;:&quot;1747216515868-is09051rd&quot;,&quot;label&quot;:&quot;Close&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1747216522954&quot;,&quot;type&quot;:&quot;Event&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;subType&quot;:&quot;PubSub&quot;,&quot;eventName&quot;:&quot;close_moda&quot;,&quot;message&quot;:&quot;close&quot;},&quot;actionIndex&quot;:1},{&quot;key&quot;:&quot;*************-4inudwnb6&quot;,&quot;label&quot;:&quot;Refresh&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-*************&quot;,&quot;type&quot;:&quot;Custom&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Record&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Record&quot;:{&quot;targetName&quot;:&quot;AccountDetails__c&quot;,&quot;targetAction&quot;:&quot;view&quot;,&quot;targetId&quot;:&quot;{recordId}&quot;}},&quot;actionIndex&quot;:2}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;flyoutDetails&quot;:{}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:12},&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;Block-1-Block-5-clone-0-Action-0-clone-0&quot;,&quot;key&quot;:&quot;element_element_element_block_1_0_block_11_0_action_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_11_0&quot;}],&quot;elementLabel&quot;:&quot;Block-1-Block-9-clone-0&quot;,&quot;key&quot;:&quot;element_element_block_1_0_block_11_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-text-align_center slds-p-around_x-small slds-m-top_large &quot;,&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;margin&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;large&quot;,&quot;label&quot;:&quot;top:large&quot;}],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;key&quot;:&quot;element_element_block_1_0_block_12_0&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;5&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-25&quot;,&quot;field&quot;:&quot;ShowTelefonoReferente&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]},&quot;data-preloadConditionalElement&quot;:false},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;5&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_5-of-12 &quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_element_block_1_0_block_7_0_baseInputElement_0_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Referente&quot;,&quot;fieldBinding&quot;:&quot;{RecAgenziaReferente}&quot;,&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1741092012497-1093uzcyw&quot;,&quot;label&quot;:&quot;ResetError&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1741092025864&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;success&quot;,&quot;fieldValue&quot;:&quot;true&quot;}]},&quot;actionIndex&quot;:0,&quot;isTrackingDisabled&quot;:true}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1741092012497-1093uzcyw&quot;,&quot;label&quot;:&quot;ResetError&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1741092025864&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;success&quot;,&quot;fieldValue&quot;:&quot;true&quot;}]},&quot;actionIndex&quot;:0,&quot;isTrackingDisabled&quot;:true}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_7_0&quot;,&quot;elementLabel&quot;:&quot;Block-1-Block-6-Text-0&quot;}],&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;elementLabel&quot;:&quot;Block-1-Block-8&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onclick&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1745578790140-vpv2qn7ye&quot;,&quot;label&quot;:&quot;Delete Ref&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1747147435749&quot;,&quot;type&quot;:&quot;DataAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;message&quot;:&quot;{\&quot;type\&quot;:\&quot;IntegrationProcedures\&quot;,\&quot;value\&quot;:{\&quot;dsDelay\&quot;:\&quot;\&quot;,\&quot;ipMethod\&quot;:\&quot;AnagDetails_RecapitiDelete\&quot;,\&quot;vlocityAsync\&quot;:false},\&quot;orderBy\&quot;:{\&quot;name\&quot;:\&quot;\&quot;,\&quot;isReverse\&quot;:\&quot;\&quot;},\&quot;contextVariables\&quot;:[]}&quot;},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;},&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-45&quot;,&quot;field&quot;:&quot;ShowTelefonoReferente&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-text-align_center slds-p-around_x-small slds-m-top_large &quot;,&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;margin&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;large&quot;,&quot;label&quot;:&quot;top:large&quot;}],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;children&quot;:[],&quot;elementLabel&quot;:&quot;Block-1-Block-11-clone-0&quot;,&quot;key&quot;:&quot;element_element_block_1_0_block_13_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-text-align_center slds-p-around_x-small slds-m-top_large &quot;,&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;margin&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;large&quot;,&quot;label&quot;:&quot;top:large&quot;}],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;key&quot;:&quot;element_element_block_1_0_block_14_0&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;5&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-30&quot;,&quot;field&quot;:&quot;ShowTelefonoReferente&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]},&quot;data-preloadConditionalElement&quot;:false},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_5-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;5&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_element_block_1_0_block_8_0_baseInputElement_0_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;text&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Telefono referente&quot;,&quot;fieldBinding&quot;:&quot;{RecAgenziaTelefonoReferente}&quot;,&quot;customProperties&quot;:[],&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1741092043231-gyhwoaudn&quot;,&quot;label&quot;:&quot;ResetError&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1741092053810&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;success&quot;,&quot;fieldValue&quot;:&quot;true&quot;}]},&quot;actionIndex&quot;:0,&quot;isTrackingDisabled&quot;:true}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1741092043231-gyhwoaudn&quot;,&quot;label&quot;:&quot;ResetError&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1741092053810&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;success&quot;,&quot;fieldValue&quot;:&quot;true&quot;}]},&quot;actionIndex&quot;:0,&quot;isTrackingDisabled&quot;:true}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_8_0&quot;,&quot;elementLabel&quot;:&quot;Block-1-Block-7-Text-0&quot;}],&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;elementLabel&quot;:&quot;Block-1-Block-9&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_5-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;5&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onclick&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1745578705509-hccggl3ke&quot;,&quot;label&quot;:&quot;Delete Tel Ref&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1747147342324&quot;,&quot;type&quot;:&quot;DataAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;message&quot;:&quot;{\&quot;type\&quot;:\&quot;IntegrationProcedures\&quot;,\&quot;value\&quot;:{\&quot;dsDelay\&quot;:\&quot;\&quot;,\&quot;ipMethod\&quot;:\&quot;AnagDetails_RecapitiDelete\&quot;,\&quot;vlocityAsync\&quot;:false},\&quot;orderBy\&quot;:{\&quot;name\&quot;:\&quot;\&quot;,\&quot;isReverse\&quot;:\&quot;\&quot;},\&quot;contextVariables\&quot;:[]}&quot;},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;},&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-24&quot;,&quot;field&quot;:&quot;ShowTelefonoReferente&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small slds-m-top_large &quot;,&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;margin&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;large&quot;,&quot;label&quot;:&quot;top:large&quot;}],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Action&quot;,&quot;element&quot;:&quot;action&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:12},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot; &quot;,&quot;iconName&quot;:&quot;utility:delete&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;stateObj&quot;:&quot;{record}&quot;,&quot;actionList&quot;:[{&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1753696202839&quot;,&quot;type&quot;:&quot;DataAction&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;message&quot;:&quot;{\&quot;type\&quot;:\&quot;IntegrationProcedures\&quot;,\&quot;value\&quot;:{\&quot;dsDelay\&quot;:\&quot;\&quot;,\&quot;ipMethod\&quot;:\&quot;AnagDetails_RecapitiDelete\&quot;,\&quot;vlocityAsync\&quot;:false,\&quot;inputMap\&quot;:{\&quot;userId\&quot;:\&quot;{User.userId}\&quot;,\&quot;recordId\&quot;:\&quot;{recordId}\&quot;,\&quot;RecapitoReferente\&quot;:\&quot;{RecAgenziaReferente}\&quot;,\&quot;Recapito\&quot;:\&quot;{RecAgenziaTelefonoReferente}\&quot;,\&quot;Preferred\&quot;:\&quot;{RecAgenziaTelefonoReferentePreferred}\&quot;,\&quot;Usage\&quot;:\&quot;{RecAgenziaTelefonoReferenteUsage}\&quot;,\&quot;idContatto\&quot;:\&quot;{RecAgenziaTelefonoReferenteId}\&quot;,\&quot;tipoContatto\&quot;:\&quot;TELREF\&quot;,\&quot;username\&quot;:\&quot;{inserisci.userInfo.username}\&quot;,\&quot;userIdReqBody\&quot;:\&quot;{inserisci.userInfo.userId}\&quot;,\&quot;compagnia\&quot;:\&quot;{inserisci.userInfo.compagnia}\&quot;},\&quot;jsonMap\&quot;:\&quot;{\\\&quot;User.userId\\\&quot;:\\\&quot;{User.userId}\\\&quot;,\\\&quot;recordId\\\&quot;:\\\&quot;{recordId}\\\&quot;,\\\&quot;RecAgenziaReferente\\\&quot;:\\\&quot;{RecAgenziaReferente}\\\&quot;,\\\&quot;RecAgenziaTelefonoReferenteId\\\&quot;:\\\&quot;{RecAgenziaTelefonoReferenteId}\\\&quot;,\\\&quot;RecAgenziaTelefonoReferenteUsage\\\&quot;:\\\&quot;{RecAgenziaTelefonoReferenteUsage}\\\&quot;,\\\&quot;RecAgenziaTelefonoReferentePreferred\\\&quot;:\\\&quot;{RecAgenziaTelefonoReferentePreferred}\\\&quot;,\\\&quot;RecAgenziaTelefonoReferente\\\&quot;:\\\&quot;{RecAgenziaTelefonoReferente}\\\&quot;,\\\&quot;inserisci.userInfo.username\\\&quot;:\\\&quot;{inserisci.userInfo.username}\\\&quot;,\\\&quot;inserisci.userInfo.userId\\\&quot;:\\\&quot;{inserisci.userInfo.userId}\\\&quot;,\\\&quot;inserisci.userInfo.compagnia\\\&quot;:\\\&quot;{inserisci.userInfo.compagnia}\\\&quot;}\&quot;},\&quot;orderBy\&quot;:{\&quot;name\&quot;:\&quot;\&quot;,\&quot;isReverse\&quot;:\&quot;\&quot;},\&quot;contextVariables\&quot;:[{\&quot;name\&quot;:\&quot;User.userId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:1},{\&quot;name\&quot;:\&quot;recordId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:3},{\&quot;name\&quot;:\&quot;RecAgenziaReferente\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:5},{\&quot;name\&quot;:\&quot;RecAgenziaTelefonoReferenteId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:10},{\&quot;name\&quot;:\&quot;RecAgenziaTelefonoReferenteUsage\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:11},{\&quot;name\&quot;:\&quot;RecAgenziaTelefonoReferentePreferred\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:12},{\&quot;name\&quot;:\&quot;RecAgenziaTelefonoReferente\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:13},{\&quot;name\&quot;:\&quot;inserisci.userInfo.username\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:19},{\&quot;name\&quot;:\&quot;inserisci.userInfo.userId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:20},{\&quot;name\&quot;:\&quot;inserisci.userInfo.compagnia\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:21}]}&quot;},&quot;key&quot;:&quot;1747216109512-bplpjkmra&quot;,&quot;label&quot;:&quot;Delete Tel Ref&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;actionIndex&quot;:0},{&quot;key&quot;:&quot;1747216564432-kgxm3m1yp&quot;,&quot;label&quot;:&quot;Close&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1747216578835&quot;,&quot;type&quot;:&quot;Event&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;subType&quot;:&quot;PubSub&quot;,&quot;eventName&quot;:&quot;close_moda&quot;,&quot;message&quot;:&quot;close&quot;},&quot;actionIndex&quot;:1},{&quot;key&quot;:&quot;*************-jkogpd2k3&quot;,&quot;label&quot;:&quot;Refresh&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-*************&quot;,&quot;type&quot;:&quot;Custom&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Record&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Record&quot;:{&quot;targetName&quot;:&quot;AccountDetails__c&quot;,&quot;targetId&quot;:&quot;{recordId}&quot;,&quot;targetAction&quot;:&quot;view&quot;}},&quot;actionIndex&quot;:2}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;flyoutDetails&quot;:{}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:12},&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_center &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-1-Block-9-clone-0-Action-0-clone-0&quot;,&quot;key&quot;:&quot;element_element_element_block_1_0_block_15_0_action_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_15_0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:12},&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_center &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]}],&quot;elementLabel&quot;:&quot;Block-1-Block-13-clone-0&quot;,&quot;key&quot;:&quot;element_element_block_1_0_block_15_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small slds-m-top_large &quot;,&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;margin&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;large&quot;,&quot;label&quot;:&quot;top:large&quot;}],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small slds-m-top_small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;top:small&quot;}],&quot;background&quot;:{&quot;color&quot;:&quot;#F3F3F3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#F3F3F3;      \n         &quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_element_block_1_0_block_3_0_outputField_0_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%3Cspan%20style=%22font-size:%2010pt;%22%3E%3Cstrong%3EPropagazione%3C/strong%3E%3C/span%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_3_0&quot;,&quot;elementLabel&quot;:&quot;Block-3-Text-0&quot;}],&quot;elementLabel&quot;:&quot;Block-1-Block-10&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small slds-m-top_small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;small&quot;,&quot;label&quot;:&quot;top:small&quot;}],&quot;background&quot;:{&quot;color&quot;:&quot;#F3F3F3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#F3F3F3;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_1_0_block_16_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;},{&quot;key&quot;:&quot;element_element_block_1_0_block_17_0&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-12&quot;,&quot;field&quot;:&quot;inserisci.checkCp.customerPermissionCheck.TabsUnipolSai&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-13&quot;,&quot;field&quot;:&quot;inserisci.checkCp.customerPermissionCheck.TabsUniSalute&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Checkbox&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;checkbox&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;checked&quot;:false,&quot;label&quot;:&quot;Propaga le modifica anche su altre compagnie&quot;,&quot;fieldBinding&quot;:&quot;{Propaga}&quot;,&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1737455897569-0njgoisuu&quot;,&quot;label&quot;:&quot;set true&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1744046717316&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;inserisci.DisableCompany&quot;,&quot;fieldValue&quot;:&quot;true&quot;}],&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;Propaga&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;false&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;actionIndex&quot;:0},{&quot;key&quot;:&quot;1737455977202-21isfi3es&quot;,&quot;label&quot;:&quot;set false&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1744045614649&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;inserisci.DisableCompany&quot;,&quot;fieldValue&quot;:&quot;false&quot;}],&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-7&quot;,&quot;field&quot;:&quot;Propaga&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;actionIndex&quot;:1}],&quot;showSpinner&quot;:&quot;false&quot;},&quot;customProperties&quot;:[{&quot;label&quot;:&quot;readOnly&quot;,&quot;value&quot;:&quot;{inserisci.DisablePropagation}&quot;,&quot;id&quot;:0}]},&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1737455897569-0njgoisuu&quot;,&quot;label&quot;:&quot;set true&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1744046717316&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;inserisci.DisableCompany&quot;,&quot;fieldValue&quot;:&quot;true&quot;}],&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;Propaga&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;false&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;actionIndex&quot;:0},{&quot;key&quot;:&quot;1737455977202-21isfi3es&quot;,&quot;label&quot;:&quot;set false&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1744045614649&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;inserisci.DisableCompany&quot;,&quot;fieldValue&quot;:&quot;false&quot;}],&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-7&quot;,&quot;field&quot;:&quot;Propaga&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;actionIndex&quot;:1}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-1-Checkbox-4&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_1_0_block_10_0_baseInputElement_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_10_0&quot;}],&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;elementLabel&quot;:&quot;Block-1-Block-11&quot;},{&quot;key&quot;:&quot;element_element_block_1_0_block_18_0&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-40&quot;,&quot;field&quot;:&quot;inserisci.checkCp.customerPermissionCheck.TabsUniSalute&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;false&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-41&quot;,&quot;field&quot;:&quot;TabsUnipolSai&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;false&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;}]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Checkbox&quot;,&quot;element&quot;:&quot;baseInputElement&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;checkbox&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;checked&quot;:false,&quot;label&quot;:&quot;Propaga le modifica anche su altre compagnie&quot;,&quot;fieldBinding&quot;:&quot;&quot;,&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1737455897569-0njgoisuu&quot;,&quot;label&quot;:&quot;set true&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1744046717316&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;inserisci.DisableCompany&quot;,&quot;fieldValue&quot;:&quot;true&quot;}],&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;Propaga&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;false&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;actionIndex&quot;:0},{&quot;key&quot;:&quot;1737455977202-21isfi3es&quot;,&quot;label&quot;:&quot;set false&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1744045614649&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;inserisci.DisableCompany&quot;,&quot;fieldValue&quot;:&quot;false&quot;}],&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-7&quot;,&quot;field&quot;:&quot;Propaga&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;actionIndex&quot;:1}],&quot;showSpinner&quot;:&quot;false&quot;},&quot;customProperties&quot;:[{&quot;label&quot;:&quot;readOnly&quot;,&quot;value&quot;:&quot;{inserisci.DisablePropagation}&quot;,&quot;id&quot;:0}],&quot;disabled&quot;:true},&quot;action&quot;:{&quot;label&quot;:&quot;Action&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;eventType&quot;:&quot;onchange&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1737455897569-0njgoisuu&quot;,&quot;label&quot;:&quot;set true&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1744046717316&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;inserisci.DisableCompany&quot;,&quot;fieldValue&quot;:&quot;true&quot;}],&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;Propaga&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;false&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;actionIndex&quot;:0},{&quot;key&quot;:&quot;1737455977202-21isfi3es&quot;,&quot;label&quot;:&quot;set false&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1744045614649&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;inserisci.DisableCompany&quot;,&quot;fieldValue&quot;:&quot;false&quot;}],&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-7&quot;,&quot;field&quot;:&quot;Propaga&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;actionIndex&quot;:1}],&quot;showSpinner&quot;:&quot;false&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-1-Checkbox-4&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;,&quot;label&quot;:&quot;bottom:xx-small&quot;}],&quot;class&quot;:&quot;slds-m-bottom_xx-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_1_0_block_18_0_baseInputElement_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_18_0&quot;}],&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;elementLabel&quot;:&quot;Block-1-Block-17-clone-0&quot;},{&quot;key&quot;:&quot;element_element_block_1_0_block_19_0&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]}},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Multi-Select&quot;,&quot;element&quot;:&quot;flexMultiSelectInput&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;type&quot;:&quot;pill&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;propertyObj&quot;:{&quot;label&quot;:&quot;Compagnia&quot;,&quot;fieldBinding&quot;:&quot;{Companies}&quot;,&quot;displayMode&quot;:&quot;Combobox&quot;,&quot;value&quot;:&quot;&quot;,&quot;options&quot;:[],&quot;customProperties&quot;:[{&quot;label&quot;:&quot;readOnly&quot;,&quot;value&quot;:&quot;{inserisci.DisableCompany}&quot;,&quot;id&quot;:0},{&quot;label&quot;:&quot;options&quot;,&quot;value&quot;:&quot;{inserisci.PropOptions}&quot;,&quot;id&quot;:1}]}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;right&quot;,&quot;size&quot;:&quot;small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;xx-small&quot;}],&quot;class&quot;:&quot;slds-p-right_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;Block-1-Block-11-Multi-Select-1&quot;,&quot;key&quot;:&quot;element_element_element_block_1_0_block_11_0_flexMultiSelectInput_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_1_0_block_11_0&quot;}],&quot;parentElementKey&quot;:&quot;element_block_1_0&quot;,&quot;elementLabel&quot;:&quot;Block-1-Block-12&quot;}],&quot;elementLabel&quot;:&quot;Block-1&quot;},{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-border_top slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#F3F3F3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_top&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#F3F3F3;     border-top: #cccccc 2px solid; \n         &quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;}},&quot;children&quot;:[{&quot;name&quot;:&quot;Action&quot;,&quot;element&quot;:&quot;action&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Annulla&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;stateObj&quot;:&quot;{record}&quot;,&quot;actionList&quot;:[{&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1737378659673&quot;,&quot;type&quot;:&quot;Event&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;subType&quot;:&quot;PubSub&quot;,&quot;eventName&quot;:&quot;close_modal&quot;,&quot;message&quot;:&quot;close&quot;},&quot;key&quot;:&quot;1737378648001-kj0z3uvt5&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;hideActionIcon&quot;:true,&quot;displayAsButton&quot;:true,&quot;buttonVariant&quot;:&quot;neutral&quot;,&quot;styles&quot;:{&quot;label&quot;:{&quot;textAlign&quot;:&quot;right&quot;}},&quot;disabled&quot;:&quot;&quot;,&quot;flyoutDetails&quot;:{}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-2-Action-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_2_0_block_1_0_action_0_0&quot;}],&quot;elementLabel&quot;:&quot;Block-2&quot;,&quot;key&quot;:&quot;element_element_block_2_0_block_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_2_0&quot;},{&quot;key&quot;:&quot;element_element_block_2_0_block_1_0&quot;,&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;}},&quot;children&quot;:[{&quot;name&quot;:&quot;Action&quot;,&quot;element&quot;:&quot;action&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Conferma&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;stateObj&quot;:&quot;{record}&quot;,&quot;actionList&quot;:[{&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1750429358314&quot;,&quot;type&quot;:&quot;DataAction&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;message&quot;:&quot;{\&quot;type\&quot;:\&quot;IntegrationProcedures\&quot;,\&quot;value\&quot;:{\&quot;dsDelay\&quot;:\&quot;\&quot;,\&quot;ipMethod\&quot;:\&quot;AnagDetails_RecapitiUpdate\&quot;,\&quot;vlocityAsync\&quot;:false,\&quot;inputMap\&quot;:{\&quot;RecAgenziaCellPersonale\&quot;:\&quot;{RecAgenziaCellPersonale}\&quot;,\&quot;RecAgenziaCellPersonalePreferred\&quot;:\&quot;{RecAgenziaCellPersonalePreferred}\&quot;,\&quot;RecAgenziaCellPersonaleUsage\&quot;:\&quot;{RecAgenziaCellPersonaleUsage}\&quot;,\&quot;RecAgenziaCellPersonaleId\&quot;:\&quot;{RecAgenziaCellPersonaleId}\&quot;,\&quot;RecAgenziaEmailPers\&quot;:\&quot;{RecAgenziaEmailPers}\&quot;,\&quot;RecAgenziaEmailPersPreferred\&quot;:\&quot;{RecAgenziaEmailPersPreferred}\&quot;,\&quot;RecAgenziaEmailPersUsage\&quot;:\&quot;{RecAgenziaEmailPersUsage}\&quot;,\&quot;RecAgenziaEmailPersId\&quot;:\&quot;{RecAgenziaEmailPersId}\&quot;,\&quot;RecAgenziaPEC\&quot;:\&quot;{RecAgenziaPEC}\&quot;,\&quot;RecAgenziaPECPreferred\&quot;:\&quot;{RecAgenziaPECPreferred}\&quot;,\&quot;RecAgenziaPECUsage\&quot;:\&quot;{RecAgenziaPECUsage}\&quot;,\&quot;RecAgenziaPECId\&quot;:\&quot;{RecAgenziaPECId}\&quot;,\&quot;RecAgenziaFax\&quot;:\&quot;{RecAgenziaFax}\&quot;,\&quot;RecAgenziaFaxPreferred\&quot;:\&quot;{RecAgenziaFaxPreferred}\&quot;,\&quot;RecAgenziaFaxUsage\&quot;:\&quot;{RecAgenziaFaxUsage}\&quot;,\&quot;RecAgenziaFaxId\&quot;:\&quot;{RecAgenziaFaxId}\&quot;,\&quot;RecAgenziaTelefono\&quot;:\&quot;{RecAgenziaTelefono}\&quot;,\&quot;RecAgenziaTelefonoPreferred\&quot;:\&quot;{RecAgenziaTelefonoPreferred}\&quot;,\&quot;RecAgenziaTelefonoUsage\&quot;:\&quot;{RecAgenziaTelefonoUsage}\&quot;,\&quot;RecAgenziaTelefonoId\&quot;:\&quot;{RecAgenziaTelefonoId}\&quot;,\&quot;RecAgenziaTelefonoReferente\&quot;:\&quot;{RecAgenziaTelefonoReferente}\&quot;,\&quot;RecAgenziaTelefonoReferentePreferred\&quot;:\&quot;{RecAgenziaTelefonoReferentePreferred}\&quot;,\&quot;RecAgenziaTelefonoReferenteUsage\&quot;:\&quot;{RecAgenziaTelefonoReferenteUsage}\&quot;,\&quot;RecAgenziaTelefonoReferenteId\&quot;:\&quot;{RecAgenziaTelefonoReferenteId}\&quot;,\&quot;RecAgenziaReferente\&quot;:\&quot;{RecAgenziaReferente}\&quot;,\&quot;recordId\&quot;:\&quot;{recordId}\&quot;,\&quot;userId\&quot;:\&quot;{User.userId}\&quot;,\&quot;username\&quot;:\&quot;{inserisci.userInfo.username}\&quot;,\&quot;userIdReqBody\&quot;:\&quot;{inserisci.userInfo.userId}\&quot;,\&quot;compagnia\&quot;:\&quot;{inserisci.userInfo.compagnia}\&quot;},\&quot;jsonMap\&quot;:\&quot;{\\\&quot;RecAgenziaCellulare\\\&quot;:\\\&quot;{RecAgenziaCellulare}\\\&quot;,\\\&quot;RecAgenziaCellularePreferred\\\&quot;:\\\&quot;{RecAgenziaCellularePreferred}\\\&quot;,\\\&quot;RecAgenziaCellulareUsage\\\&quot;:\\\&quot;{RecAgenziaCellulareUsage}\\\&quot;,\\\&quot;RecAgenziaCellulareId\\\&quot;:\\\&quot;{RecAgenziaCellulareId}\\\&quot;,\\\&quot;RecAgenziaEmail\\\&quot;:\\\&quot;{RecAgenziaEmail}\\\&quot;,\\\&quot;RecAgenziaEmailPreferred\\\&quot;:\\\&quot;{RecAgenziaEmailPreferred}\\\&quot;,\\\&quot;RecAgenziaEmailUsage\\\&quot;:\\\&quot;{RecAgenziaEmailUsage}\\\&quot;,\\\&quot;RecAgenziaEmailId\\\&quot;:\\\&quot;{RecAgenziaEmailId}\\\&quot;,\\\&quot;RecAgenziaPEC\\\&quot;:\\\&quot;{RecAgenziaPEC}\\\&quot;,\\\&quot;RecAgenziaPECPreferred\\\&quot;:\\\&quot;{RecAgenziaPECPreferred}\\\&quot;,\\\&quot;RecAgenziaPECUsage\\\&quot;:\\\&quot;{RecAgenziaPECUsage}\\\&quot;,\\\&quot;RecAgenziaPECId\\\&quot;:\\\&quot;{RecAgenziaPECId}\\\&quot;,\\\&quot;RecAgenziaFax\\\&quot;:\\\&quot;{RecAgenziaFax}\\\&quot;,\\\&quot;RecAgenziaFaxPreferred\\\&quot;:\\\&quot;{RecAgenziaFaxPreferred}\\\&quot;,\\\&quot;RecAgenziaFaxUsage\\\&quot;:\\\&quot;{RecAgenziaFaxUsage}\\\&quot;,\\\&quot;RecAgenziaFaxId\\\&quot;:\\\&quot;{RecAgenziaFaxId}\\\&quot;,\\\&quot;RecAgenziaTelefono\\\&quot;:\\\&quot;{RecAgenziaTelefono}\\\&quot;,\\\&quot;RecAgenziaTelefonoPreferred\\\&quot;:\\\&quot;{RecAgenziaTelefonoPreferred}\\\&quot;,\\\&quot;RecAgenziaTelefonoUsage\\\&quot;:\\\&quot;{RecAgenziaTelefonoUsage}\\\&quot;,\\\&quot;RecAgenziaTelefonoId\\\&quot;:\\\&quot;{RecAgenziaTelefonoId}\\\&quot;,\\\&quot;RecAgenziaTelefonoReferente\\\&quot;:\\\&quot;{RecAgenziaTelefonoReferente}\\\&quot;,\\\&quot;RecAgenziaTelefonoReferentePreferred\\\&quot;:\\\&quot;{RecAgenziaTelefonoReferentePreferred}\\\&quot;,\\\&quot;RecAgenziaTelefonoReferenteUsage\\\&quot;:\\\&quot;{RecAgenziaTelefonoReferenteUsage}\\\&quot;,\\\&quot;RecAgenziaTelefonoReferenteId\\\&quot;:\\\&quot;{RecAgenziaTelefonoReferenteId}\\\&quot;,\\\&quot;RecAgenziaReferente\\\&quot;:\\\&quot;{RecAgenziaReferente}\\\&quot;,\\\&quot;RecAgenziaCellPersonale\\\&quot;:\\\&quot;{RecAgenziaCellPersonale}\\\&quot;,\\\&quot;RecAgenziaCellPersonalePreferred\\\&quot;:\\\&quot;{RecAgenziaCellPersonalePreferred}\\\&quot;,\\\&quot;RecAgenziaCellPersonaleUsage\\\&quot;:\\\&quot;{RecAgenziaCellPersonaleUsage}\\\&quot;,\\\&quot;RecAgenziaCellPersonaleId\\\&quot;:\\\&quot;{RecAgenziaCellPersonaleId}\\\&quot;,\\\&quot;RecAgenziaEmailPers\\\&quot;:\\\&quot;{RecAgenziaEmailPers}\\\&quot;,\\\&quot;RecAgenziaEmailPersPreferred\\\&quot;:\\\&quot;{RecAgenziaEmailPersPreferred}\\\&quot;,\\\&quot;RecAgenziaEmailPersUsage\\\&quot;:\\\&quot;{RecAgenziaEmailPersUsage}\\\&quot;,\\\&quot;RecAgenziaEmailPersId\\\&quot;:\\\&quot;{RecAgenziaEmailPersId}\\\&quot;,\\\&quot;recordId\\\&quot;:\\\&quot;{recordId}\\\&quot;,\\\&quot;User.userId\\\&quot;:\\\&quot;{User.userId}\\\&quot;,\\\&quot;recapiti.inserisci.userInfo.compagnia\\\&quot;:\\\&quot;{recapiti.inserisci.userInfo.compagnia}\\\&quot;,\\\&quot;recapiti.inserisci.userInfo.username\\\&quot;:\\\&quot;{recapiti.inserisci.userInfo.username}\\\&quot;,\\\&quot;recapiti.inserisci.userInfo.userId\\\&quot;:\\\&quot;{recapiti.inserisci.userInfo.userId}\\\&quot;,\\\&quot;inserisci.userInfo.username\\\&quot;:\\\&quot;{inserisci.userInfo.username}\\\&quot;,\\\&quot;inserisci.userInfo.userId\\\&quot;:\\\&quot;{inserisci.userInfo.userId}\\\&quot;,\\\&quot;inserisci.userInfo.compagnia\\\&quot;:\\\&quot;{inserisci.userInfo.compagnia}\\\&quot;}\&quot;,\&quot;resultVar\&quot;:\&quot;\&quot;},\&quot;orderBy\&quot;:{\&quot;name\&quot;:\&quot;\&quot;,\&quot;isReverse\&quot;:\&quot;\&quot;},\&quot;contextVariables\&quot;:[{\&quot;name\&quot;:\&quot;RecAgenziaCellulare\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:2},{\&quot;name\&quot;:\&quot;RecAgenziaCellularePreferred\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:4},{\&quot;name\&quot;:\&quot;RecAgenziaCellulareUsage\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:6},{\&quot;name\&quot;:\&quot;RecAgenziaCellulareId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:8},{\&quot;name\&quot;:\&quot;RecAgenziaEmail\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:10},{\&quot;name\&quot;:\&quot;RecAgenziaEmailPreferred\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:12},{\&quot;name\&quot;:\&quot;RecAgenziaEmailUsage\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:14},{\&quot;name\&quot;:\&quot;RecAgenziaEmailId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:16},{\&quot;name\&quot;:\&quot;RecAgenziaPEC\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:18},{\&quot;name\&quot;:\&quot;RecAgenziaPECPreferred\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:20},{\&quot;name\&quot;:\&quot;RecAgenziaPECUsage\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:22},{\&quot;name\&quot;:\&quot;RecAgenziaPECId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:24},{\&quot;name\&quot;:\&quot;RecAgenziaFax\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:26},{\&quot;name\&quot;:\&quot;RecAgenziaFaxPreferred\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:28},{\&quot;name\&quot;:\&quot;RecAgenziaFaxUsage\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:30},{\&quot;name\&quot;:\&quot;RecAgenziaFaxId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:32},{\&quot;name\&quot;:\&quot;RecAgenziaTelefono\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:34},{\&quot;name\&quot;:\&quot;RecAgenziaTelefonoPreferred\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:36},{\&quot;name\&quot;:\&quot;RecAgenziaTelefonoUsage\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:38},{\&quot;name\&quot;:\&quot;RecAgenziaTelefonoId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:40},{\&quot;name\&quot;:\&quot;RecAgenziaTelefonoReferente\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:42},{\&quot;name\&quot;:\&quot;RecAgenziaTelefonoReferentePreferred\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:44},{\&quot;name\&quot;:\&quot;RecAgenziaTelefonoReferenteUsage\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:46},{\&quot;name\&quot;:\&quot;RecAgenziaTelefonoReferenteId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:48},{\&quot;name\&quot;:\&quot;RecAgenziaReferente\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:50},{\&quot;name\&quot;:\&quot;RecAgenziaCellPersonale\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:56},{\&quot;name\&quot;:\&quot;RecAgenziaCellPersonalePreferred\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:57},{\&quot;name\&quot;:\&quot;RecAgenziaCellPersonaleUsage\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:58},{\&quot;name\&quot;:\&quot;RecAgenziaCellPersonaleId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:59},{\&quot;name\&quot;:\&quot;RecAgenziaEmailPers\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:60},{\&quot;name\&quot;:\&quot;RecAgenziaEmailPersPreferred\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:61},{\&quot;name\&quot;:\&quot;RecAgenziaEmailPersUsage\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:62},{\&quot;name\&quot;:\&quot;RecAgenziaEmailPersId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:63},{\&quot;name\&quot;:\&quot;recordId\&quot;,\&quot;val\&quot;:\&quot;a1i9X0000050jblQAA\&quot;,\&quot;id\&quot;:81},{\&quot;name\&quot;:\&quot;User.userId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:82},{\&quot;name\&quot;:\&quot;recapiti.inserisci.userInfo.compagnia\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:57},{\&quot;name\&quot;:\&quot;recapiti.inserisci.userInfo.username\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:58},{\&quot;name\&quot;:\&quot;recapiti.inserisci.userInfo.userId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:59},{\&quot;name\&quot;:\&quot;inserisci.userInfo.username\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:120},{\&quot;name\&quot;:\&quot;inserisci.userInfo.userId\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:121},{\&quot;name\&quot;:\&quot;inserisci.userInfo.compagnia\&quot;,\&quot;val\&quot;:\&quot;\&quot;,\&quot;id\&quot;:122}]}&quot;,&quot;responseNode&quot;:&quot;record&quot;},&quot;key&quot;:&quot;1741079595764-m5dztx8t6&quot;,&quot;label&quot;:&quot;Send&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:true,&quot;actionIndex&quot;:0,&quot;isTrackingDisabled&quot;:true},{&quot;key&quot;:&quot;1741091214707-3qkvpxldr&quot;,&quot;label&quot;:&quot;Close&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1742665713464&quot;,&quot;type&quot;:&quot;Event&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;subType&quot;:&quot;PubSub&quot;,&quot;eventName&quot;:&quot;close_moda&quot;,&quot;message&quot;:&quot;close&quot;,&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;success&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;actionIndex&quot;:1,&quot;isTrackingDisabled&quot;:true},{&quot;key&quot;:&quot;*************-yy26khgi7&quot;,&quot;label&quot;:&quot;Refresh&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-*************&quot;,&quot;type&quot;:&quot;Custom&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Record&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Record&quot;:{&quot;targetName&quot;:&quot;AccountDetails__c&quot;,&quot;targetAction&quot;:&quot;view&quot;,&quot;targetId&quot;:&quot;{recordId}&quot;},&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;success&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;actionIndex&quot;:2}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;hideActionIcon&quot;:true,&quot;displayAsButton&quot;:true,&quot;buttonVariant&quot;:&quot;brand&quot;,&quot;flyoutDetails&quot;:{}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-3-Action-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_element_block_2_0_block_1_0_action_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_element_block_2_0_block_1_0&quot;}],&quot;parentElementKey&quot;:&quot;element_block_2_0&quot;,&quot;elementLabel&quot;:&quot;Block-3-Block-2&quot;}],&quot;elementLabel&quot;:&quot;Block-2&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-border_top slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#F3F3F3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;border_top&quot;,&quot;width&quot;:&quot;2&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#F3F3F3;     border-top: #cccccc 2px solid; \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]}]}},&quot;childCards&quot;:[],&quot;actions&quot;:[],&quot;omniscripts&quot;:[],&quot;documents&quot;:[]}],&quot;dataSource&quot;:{&quot;type&quot;:null,&quot;value&quot;:{},&quot;orderBy&quot;:{},&quot;contextVariables&quot;:[]},&quot;title&quot;:&quot;DA_Recapiti_Modifica&quot;,&quot;enableLwc&quot;:true,&quot;isFlex&quot;:true,&quot;theme&quot;:&quot;slds&quot;,&quot;selectableMode&quot;:&quot;Multi&quot;,&quot;lwc&quot;:{&quot;DeveloperName&quot;:&quot;cfDA_Recapiti_Agenzia_Modifica_12_Unipolsai&quot;,&quot;Id&quot;:&quot;0Rb9V000003ZVyTSAW&quot;,&quot;MasterLabel&quot;:&quot;cfDA_Recapiti_Agenzia_Modifica_12_Unipolsai&quot;,&quot;NamespacePrefix&quot;:&quot;c&quot;,&quot;ManageableState&quot;:&quot;unmanaged&quot;},&quot;osSupport&quot;:true,&quot;sessionVars&quot;:[],&quot;xmlObject&quot;:{&quot;masterLabel&quot;:&quot;DA_Recapiti_Modifica&quot;,&quot;isExposed&quot;:true,&quot;apiVersion&quot;:56}}</propertySetConfig>
    <versionNumber>13</versionNumber>
</OmniUiCard>
