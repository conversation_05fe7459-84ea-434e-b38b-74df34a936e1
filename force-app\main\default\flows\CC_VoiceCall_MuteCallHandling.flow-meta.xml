<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>64.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <decisions>
        <name>DEC_IsAlreadyMute</name>
        <label>DEC_IsAlreadyMute</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>FALSE</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>GET_RelatedCase.IsMuteCall__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>UPD_SetMuteFields</targetReference>
            </connector>
            <label>FALSE</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <formulas>
        <name>FML_BlacklistUntil_Plus5</name>
        <dataType>Date</dataType>
        <expression>$Flow.CurrentDate + 5</expression>
    </formulas>
    <interviewLabel>CC_VoiceCall_MuteCallHandling {!$Flow.CurrentDateTime}</interviewLabel>
    <label>CC_VoiceCall_MuteCallHandling</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>GET_RelatedCase</name>
        <label>GET_RelatedCase</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>DEC_IsAlreadyMute</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record__Prior.RelatedCase__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>UPD_SetMuteFields</name>
        <label>UPD_SetMuteFields</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <inputAssignments>
            <field>IsMuteCall__c</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>MuteBlacklistUntil__c</field>
            <value>
                <elementReference>FML_BlacklistUntil_Plus5</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>MuteHandled__c</field>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputAssignments>
        <inputReference>$Record.RelatedCase__r</inputReference>
    </recordUpdates>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>GET_RelatedCase</targetReference>
        </connector>
        <filterFormula>AND(
  NOT(ISBLANK({!$Record.RelatedCase__c})),               
  LOWER({!$Record.MuteCall__c}) = &quot;true&quot;,         
  {!$Record.RecordType.Name} = &quot;CC_Contact_Center&quot;
)</filterFormula>
        <object>VoiceCall</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
