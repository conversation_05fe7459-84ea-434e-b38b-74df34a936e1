/*
* @description This class is used to update a User after SSO Login
* @cicd_tests UserProvisioningHandler_Test
*/
public without sharing class UserProvisioningUpdateRental {

    public class UCAResult{
        public List<UserType> users = new List<UserType>();
    }

    public class UserType{
        public String userId                {get; set;}
        public List<String> groups          {get; set;}
        public List<String> cin             {get; set;}
    }
    
    /*
    * @description This method is called to get User Context (from UCA API) and to set Permission Set on the User
    * @param User u instance of the existing User
    * @param Map<String, String> attributes Map Key-->Value saml request attriute --> value of the attribute
    */
    public static void handleUser(User u, Map<String, String> attributes) {
        
        //List<QueueableUserProvisioning.InputWrapper> queueableInputs = new List<QueueableUserProvisioning.InputWrapper>();
        
        try{
            
            String fiscalCode = attributes.get('CF');
            System.debug('UserProvisioningUpdateRental.fiscalCode: '+fiscalCode);
            
            String sama = attributes.get('sama');
            System.debug('UserProvisioningUpdateRental.sama: '+sama);
            
            // Callout here to get info from UCA API
            UserProvisioningAPI.UCARentalResponse UCAData = UserProvisioningAPI.getUCARentalData(fiscalCode, sama, u.Email);
            
            Map<String, UCA_Mapping__mdt> mappingUCA = UCA_Mapping__mdt.getAll();
            
            Map<String, Set<String>> mapCodePermissionSet = new Map<String, Set<String>>();
            Set<String> permissionSetNameToDelete = new Set<String>();
            Set<String> defaultPermissionSet = new Set<String>();
            for(UCA_Mapping__mdt uca : mappingUCA.values()){

                if(String.isBlank(uca.Permission_Set_Name__c)) continue;
                
                if(String.isNotBlank(uca.DeveloperName) && !uca.DeveloperName.startsWithIgnoreCase('RENTAL_')) continue;

                if(!uca.Default_Assignment__c){
                    if(String.isNotBlank(uca.Permission_Set_Name__c)){
                        permissionSetNameToDelete.addAll(uca.Permission_Set_Name__c.split(','));

                        if(mapCodePermissionSet.containsKey('CN='+uca.Code__c)){
                            mapCodePermissionSet.get('CN='+uca.Code__c).addAll(uca.Permission_Set_Name__c.split(','));
                        }else{
                            Set<String> tempSet = new Set<String>();
                            tempSet.addAll(uca.Permission_Set_Name__c.split(','));
                            mapCodePermissionSet.put('CN='+uca.Code__c, tempSet);
                        }
                    }
                }else{

                    defaultPermissionSet.addAll(uca.Permission_Set_Name__c.split(','));

                }
            }

            if(Test.isRunningTest()){
                mapCodePermissionSet.put('CN=0000',new Set<String>{'UNIT_TEST_CLASS'});
            }
            
            Set<String> permissionSetToDo = new Set<String>();

            UserProvisioningUpdate.UCAResult jsonParser = new UserProvisioningUpdate.UCAResult();
            jsonParser.users = new List<UserProvisioningUpdate.UserType>();

            for(UserProvisioningAPI.UCARentalItem uri : UCAData.utenti){

                if(uri.gruppi != null && !uri.gruppi.isEmpty()){

                    for(String s : uri.gruppi){
						                 
						List<String> splitComma = s.split(',');
						
                        for(String str : splitComma){
	                        if(mapCodePermissionSet.containsKey(s)){
    	                        permissionSetToDo.addAll(mapCodePermissionSet.get(s));
                                break;
        	                }
                        }

                    }

                }
                
            }

            Set<String> mergeSet = new Set<String>();
            if(!defaultPermissionSet.isEmpty()) mergeSet.addAll(defaultPermissionSet);
            if(!permissionSetToDo.isEmpty()) mergeSet.addAll(permissionSetToDo);
            
            if(!mergeSet.isEmpty()){
                
                List<PermissionSetAssignment> assignmentToInsert = new List<PermissionSetAssignment>();
            
                List<PermissionSet> listPermissionSet = [SELECT Id, Name FROM PermissionSet WHERE Name IN :mergeSet];
                Map<String, Id> mapPermissionSetId = new Map<String, Id>();
                for(PermissionSet ps : listPermissionSet){
                    mapPermissionSetId.put(ps.Name, ps.Id);
                }
                
                for(String s : mergeSet){
                    
                    if(mapPermissionSetId.containsKey(s)){
                        
                        assignmentToInsert.add(
                            UserProvisioningUtils.createPermissionSetAssignment(
                                u.Id, 
                                mapPermissionSetId.get(s)
                            )
                        );
                        
                    }
                }
                
                // Delete old permission set assignment
                List<PermissionSetAssignment> assignmentToDelete = [SELECT Id FROM PermissionSetAssignment WHERE AssigneeId = :u.Id AND PermissionSet.Name IN :permissionSetNameToDelete];
                if(!assignmentToDelete.isEmpty()) Database.delete(assignmentToDelete, false);

                // Insert new permission set assignment
                if(!assignmentToInsert.isEmpty()) Database.insert(assignmentToInsert, false);

                //u.UCA_Permissions__c = JSON.serialize(jsonParser);
				u.SAMA__c = sama;
                update u;
                
                callFutureMethod(u.Id);
                
            }
        }catch(Exception ex){
            System.debug('UserProvisioningUpdate Exception message: '+ex.getMessage());
            System.debug('UserProvisioningUpdate Exception stack trace: '+ex.getStackTraceString());
            //throw ex;
        }
        
    }
    
    @future
    public static void callFutureMethod(String userId){
        urcs_GroupResolver.assignPublicGroupMembership(userId);
    }

}