/**
 * @File Name         : urcs_DC_GetServizi_Test.cls
 * @Description       : Test class for urcs_DC_GetServizi
 * <AUTHOR> VE
 * @Group             :
 * @Last Modified On  : 15-09-2025
 * @Last Modified By  : VE
**/
@isTest
public class urcs_DC_GetServizi_Test {
    
    @TestSetup
    static void makeData(){
        Account testAccount = new Account();
        testAccount.Name = 'Test Account';
        insert testAccount;

        ServiceContract testServiceContract = new ServiceContract();
        testServiceContract.Name = '12345';
        testServiceContract.AccountId = testAccount.Id;
        testServiceContract.ExternalId__c = 'UR_12345';
        insert testServiceContract;
    }
    
    @isTest
    static void testCallMethod() {
        ServiceContract testServiceContract = [SELECT Id FROM ServiceContract WHERE Name = '12345' LIMIT 1];

        Map<String, Object> input = new Map<String, Object>{
            'recordId' => testServiceContract.Id,
            'Set Values' => new Map<String, Object>{
                'methodExecute' => 'init'
            }
        };

        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        Map<String, Object> args = new Map<String, Object>{
            'input' => input,
            'output' => output,
            'options' => options
        };

        urcs_DC_GetServizi controller = new urcs_DC_GetServizi();

        Test.startTest();
        Object result = controller.call('testAction', args);
        Test.stopTest();
    }
    
    @isTest
    static void testInvokeMethod() {
        ServiceContract testServiceContract = [SELECT Id FROM ServiceContract WHERE Name = '12345' LIMIT 1];

        Map<String, Object> input = new Map<String, Object>{
            'recordId' => testServiceContract.Id,
            'Set Values' => new Map<String, Object>{
                'methodExecute' => 'init'
            }
        };

        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        Test.startTest();
        Map<String, Object> result = urcs_DC_GetServizi.invokeMethod(input, output, options);
        Test.stopTest();
    }

    @isTest
    static void testGetServizi() {
        ServiceContract testServiceContract = [SELECT Id FROM ServiceContract WHERE Name = '12345' LIMIT 1];

        Map<String, Object> input = new Map<String, Object>{
            'recordId' => testServiceContract.Id
        };

        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        Test.startTest();
        try{
            Map<String, Object> result = urcs_DC_GetServizi.getServizi(input, output, options);
        }catch(Exception ex){}
        Test.stopTest();
    }
    
    @isTest
    static void testGetServiziWithInvalidContract() {
        String fakeContractId = '801000000000000AAA';

        Map<String, Object> input = new Map<String, Object>{
            'recordId' => fakeContractId
        };

        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        Test.startTest();
        try{
            Map<String, Object> result = urcs_DC_GetServizi.getServizi(input, output, options);
        }catch(Exception ex){}
        Test.stopTest();
    }
    
    @isTest
    static void testGetServiziWithNullRecordId() {
        Map<String, Object> input = new Map<String, Object>{
            'recordId' => null
        };
        
        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();
        
        Test.startTest();
        try{
            Map<String, Object> result = urcs_DC_GetServizi.getServizi(input, output, options);
        }catch(Exception ex){}
        Test.stopTest();
    }
    
    @isTest
    static void testInvokeMethodException() {
        Map<String, Object> input = new Map<String, Object>{
            'recordId' => 'invalid',
            'Set Values' => new Map<String, Object>{
                'methodExecute' => 'init'
            }
        };

        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        Test.startTest();
        try{
            Map<String, Object> result = urcs_DC_GetServizi.invokeMethod(input, output, options);
        }catch(Exception ex){}
        Test.stopTest();
    }

    @isTest
    static void testGetServiziWithNonNumericContractName() {
        // Crea un ServiceContract con nome non numerico per testare la gestione dell'errore
        Account testAccount = new Account();
        testAccount.Name = 'Test Account Non Numeric';
        insert testAccount;

        ServiceContract testServiceContract = new ServiceContract();
        testServiceContract.Name = 'NonNumericName'; // Nome non convertibile in Integer
        testServiceContract.AccountId = testAccount.Id;
        testServiceContract.ExternalId__c = 'UR_NONNUMERIC';
        insert testServiceContract;

        Map<String, Object> input = new Map<String, Object>{
            'recordId' => testServiceContract.Id
        };

        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        Test.startTest();
        try{
            Map<String, Object> result = urcs_DC_GetServizi.getServizi(input, output, options);
            // Verifica che l'output contenga null per data a causa dell'errore
            System.assertEquals(null, result.get('data'), 'Data should be null due to conversion error');
        }catch(Exception ex){
            // L'eccezione è gestita nel metodo, quindi non dovrebbe propagarsi
        }
        Test.stopTest();
    }

    @isTest
    static void testGetServiziWithValidNumericContractName() {
        // Test specifico per verificare che la conversione Integer.valueOf funzioni correttamente
        ServiceContract testServiceContract = [SELECT Id, Name FROM ServiceContract WHERE Name = '12345' LIMIT 1];

        Map<String, Object> input = new Map<String, Object>{
            'recordId' => testServiceContract.Id
        };

        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        Test.startTest();
        try{
            Map<String, Object> result = urcs_DC_GetServizi.getServizi(input, output, options);
            // Verifica che il metodo sia eseguito senza errori
            System.assertNotEquals(null, result, 'Result should not be null');
            // Il data potrebbe essere una lista vuota se non ci sono record UR_SERVIZI__dlm corrispondenti
            System.assert(result.containsKey('data'), 'Result should contain data key');
        }catch(Exception ex){
            System.assert(false, 'No exception should be thrown with valid numeric contract name: ' + ex.getMessage());
        }
        Test.stopTest();
    }
}