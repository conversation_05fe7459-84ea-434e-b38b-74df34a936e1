<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>InsurancePolicyCreateSharingRule</name>
        <label>InsurancePolicyCreateSharingRule</label>
        <locationX>1942</locationX>
        <locationY>924</locationY>
        <actionName>InsurancePolicyCreateSharingRule</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Check_If_Is_Error</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>accountId</name>
            <value>
                <elementReference>$Record.NameInsuredId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>groupId</name>
            <value>
                <elementReference>GetCIPGrp.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>InsurancePolicyCreateSharingRule</nameSegment>
        <offset>0</offset>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </actionCalls>
    <apiVersion>62.0</apiVersion>
    <assignments>
        <name>Add_id_to_list</name>
        <label>Add id to list</label>
        <locationX>754</locationX>
        <locationY>3096</locationY>
        <assignmentItems>
            <assignToReference>prodIdsList</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_Through_Prodotto_Opportunities.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Through_Prodotto_Opportunities</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Areas_of_Need</name>
        <label>Areas of Need</label>
        <locationX>490</locationX>
        <locationY>3828</locationY>
        <assignmentItems>
            <assignToReference>AreasOfNeedOfSoldQuotes</assignToReference>
            <operator>AddItem</operator>
            <value>
                <elementReference>Loop_Through_Sold_Quotes.AreasOfNeed__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Through_Sold_Quotes</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Areas_of_Need_of_active_or_sold_quotes_not_stored</name>
        <label>Areas of Need of active or sold quotes not stored</label>
        <locationX>842</locationX>
        <locationY>3828</locationY>
        <assignmentItems>
            <assignToReference>AreasOfNeedOfActiveOrSoldQuotesNotStored</assignToReference>
            <operator>AddItem</operator>
            <value>
                <elementReference>Loop_Through_Active_Or_Sold_Quotes_Not_Stored.AreasOfNeed__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Through_Active_Or_Sold_Quotes_Not_Stored</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assignment_Source</name>
        <label>Assignment Source</label>
        <locationX>798</locationX>
        <locationY>600</locationY>
        <assignmentItems>
            <assignToReference>returnQuote</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Related_Source</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_Quote</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Number_of_Sold_Quotes</name>
        <label>Number of Sold Quotes</label>
        <locationX>578</locationX>
        <locationY>2172</locationY>
        <assignmentItems>
            <assignToReference>NumberOfSoldQuotes</assignToReference>
            <operator>AssignCount</operator>
            <value>
                <elementReference>Sold_quotes</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_difference_Between_total_and_sold_quotes</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Number_of_Total_Quotes</name>
        <label>Number of Total Quotes</label>
        <locationX>578</locationX>
        <locationY>2064</locationY>
        <assignmentItems>
            <assignToReference>NumberOfTotalQuotes</assignToReference>
            <operator>AssignCount</operator>
            <value>
                <elementReference>Get_all_quotes_from_opp_prod</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Number_of_Sold_Quotes</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Paste_Channel4Opp_when_is_present</name>
        <label>Paste Channel4Opp when is present</label>
        <locationX>446</locationX>
        <locationY>1248</locationY>
        <assignmentItems>
            <assignToReference>channel4Opp</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.Channel__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Relate_Quote_to_Policy</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Populate_Channel4Opp</name>
        <label>Populate Channel4Opp</label>
        <locationX>710</locationX>
        <locationY>1248</locationY>
        <assignmentItems>
            <assignToReference>channel4Opp</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Record.Channel__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Relate_Quote_to_Policy</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Sum_of_all_active_or_sold_quotes_not_stored</name>
        <label>Sum of all active or sold quotes not stored</label>
        <locationX>842</locationX>
        <locationY>3720</locationY>
        <assignmentItems>
            <assignToReference>SumOfActiveOrSoldQuotesNotStored</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_Through_Active_Or_Sold_Quotes_Not_Stored.QuoteAmount__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Areas_of_Need_of_active_or_sold_quotes_not_stored</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Sum_of_all_sold_quotes</name>
        <label>Sum of all sold quotes</label>
        <locationX>490</locationX>
        <locationY>3720</locationY>
        <assignmentItems>
            <assignToReference>SumOfAllSoldQuotes</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_Through_Sold_Quotes.QuoteAmount__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Areas_of_Need</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>ThereIsAProdOppNotClosed2</name>
        <label>ThereIsAProdOppNotClosed</label>
        <locationX>842</locationX>
        <locationY>2904</locationY>
        <assignmentItems>
            <assignToReference>ThereIsAProdOppNotClosed</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Add_id_to_list</targetReference>
        </connector>
    </assignments>
    <collectionProcessors>
        <name>All</name>
        <elementSubtype>FilterCollectionProcessor</elementSubtype>
        <label>All Active Or Sold Quotes Not Stored</label>
        <locationX>754</locationX>
        <locationY>3504</locationY>
        <assignNextValueToReference>currentItem_All</assignNextValueToReference>
        <collectionProcessorType>FilterCollectionProcessor</collectionProcessorType>
        <collectionReference>Get_all_quotes_from_opp</collectionReference>
        <conditionLogic>1 AND (2 OR 3)</conditionLogic>
        <conditions>
            <leftValueReference>currentItem_All.IsStored__c</leftValueReference>
            <operator>EqualTo</operator>
            <rightValue>
                <booleanValue>false</booleanValue>
            </rightValue>
        </conditions>
        <conditions>
            <leftValueReference>currentItem_All.CommercialStatus__c</leftValueReference>
            <operator>EqualTo</operator>
            <rightValue>
                <stringValue>ACTIVE</stringValue>
            </rightValue>
        </conditions>
        <conditions>
            <leftValueReference>currentItem_All.CommercialStatus__c</leftValueReference>
            <operator>EqualTo</operator>
            <rightValue>
                <stringValue>SOLD</stringValue>
            </rightValue>
        </conditions>
        <connector>
            <targetReference>Loop_Through_Active_Or_Sold_Quotes_Not_Stored</targetReference>
        </connector>
    </collectionProcessors>
    <collectionProcessors>
        <name>All_Sold_quotes</name>
        <elementSubtype>FilterCollectionProcessor</elementSubtype>
        <label>All Sold quotes</label>
        <locationX>402</locationX>
        <locationY>3504</locationY>
        <assignNextValueToReference>currentItem_All_Sold_quotes</assignNextValueToReference>
        <collectionProcessorType>FilterCollectionProcessor</collectionProcessorType>
        <collectionReference>Get_all_quotes_from_opp</collectionReference>
        <conditionLogic>and</conditionLogic>
        <conditions>
            <leftValueReference>currentItem_All_Sold_quotes.CommercialStatus__c</leftValueReference>
            <operator>EqualTo</operator>
            <rightValue>
                <stringValue>SOLD</stringValue>
            </rightValue>
        </conditions>
        <connector>
            <targetReference>Loop_Through_Sold_Quotes</targetReference>
        </connector>
    </collectionProcessors>
    <collectionProcessors>
        <name>Sold_quotes</name>
        <elementSubtype>FilterCollectionProcessor</elementSubtype>
        <label>Sold quotes</label>
        <locationX>578</locationX>
        <locationY>1956</locationY>
        <assignNextValueToReference>currentItem_All_Sold_quotes</assignNextValueToReference>
        <collectionProcessorType>FilterCollectionProcessor</collectionProcessorType>
        <collectionReference>Get_all_quotes_from_opp_prod</collectionReference>
        <conditionLogic>and</conditionLogic>
        <conditions>
            <leftValueReference>currentItem_All_Sold_quotes.CommercialStatus__c</leftValueReference>
            <operator>EqualTo</operator>
            <rightValue>
                <stringValue>SOLD</stringValue>
            </rightValue>
        </conditions>
        <connector>
            <targetReference>Number_of_Total_Quotes</targetReference>
        </connector>
    </collectionProcessors>
    <constants>
        <name>INS_POL_RECORD_TYPE_FOLDER</name>
        <dataType>String</dataType>
        <value>
            <stringValue>PU_FOLDER</stringValue>
        </value>
    </constants>
    <customErrors>
        <name>ErrorMessage</name>
        <label>ErrorMessage</label>
        <locationX>1810</locationX>
        <locationY>1140</locationY>
        <customErrorMessages>
            <errorMessage>Errore nella creazione delle Sharing

{!InsurancePolicyCreateSharingRule.message}</errorMessage>
            <isFieldError>false</isFieldError>
        </customErrorMessages>
    </customErrors>
    <decisions>
        <name>All_Prod_Opps_Closed</name>
        <label>All Prod Opps Closed</label>
        <locationX>578</locationX>
        <locationY>3396</locationY>
        <defaultConnector>
            <targetReference>All</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>All_Prodotto_Opps_Closed</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>ThereIsAProdOppNotClosed</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>All_Sold_quotes</targetReference>
            </connector>
            <label>All Prodotto Opps Closed</label>
        </rules>
    </decisions>
    <decisions>
        <name>check_AccAccRel_GroupCIP</name>
        <label>check AccAccRel GroupCIP</label>
        <locationX>2140</locationX>
        <locationY>708</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>valid_AccAccRel</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>GetAccAccRelAgency</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>GetCIPGrp</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>InsAccAccRelAgencyShareRec</targetReference>
            </connector>
            <label>valid AccAccRel</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_difference_Between_total_and_sold_quotes</name>
        <label>Check difference Between total and sold quotes</label>
        <locationX>578</locationX>
        <locationY>2280</locationY>
        <defaultConnector>
            <targetReference>Retrieve_Policy_Channel_for_Container</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Still active quotes</defaultConnectorLabel>
        <rules>
            <name>Non_active_quotes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>NumberOfSoldQuotes</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>NumberOfTotalQuotes</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Close_Prodotto_Opportunity</targetReference>
            </connector>
            <label>Non active quotes</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_If_Is_Error</name>
        <label>Check If Is Error</label>
        <locationX>1942</locationX>
        <locationY>1032</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Error</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>InsurancePolicyCreateSharingRule.esito</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>KO</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>ErrorMessage</targetReference>
            </connector>
            <label>Error</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Ins_Policy_RecordType</name>
        <label>Check Ins Policy RecordType</label>
        <locationX>578</locationX>
        <locationY>1548</locationY>
        <defaultConnector>
            <targetReference>Get_all_quotes_from_opp_prod</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Position</defaultConnectorLabel>
        <rules>
            <name>Folder</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>INS_POL_RECORD_TYPE_FOLDER</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Create_Contact_History</targetReference>
            </connector>
            <label>Folder</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Opp_Canale_di_Vendita</name>
        <label>Check Opp Canale di Vendita</label>
        <locationX>578</locationX>
        <locationY>1140</locationY>
        <defaultConnector>
            <targetReference>Populate_Channel4Opp</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Canale vendita</defaultConnectorLabel>
        <rules>
            <name>Canale_Is_Present</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Quote_Opportunity.PolicyChannel__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Paste_Channel4Opp_when_is_present</targetReference>
            </connector>
            <label>Canale vendita Is Present</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Quote</name>
        <label>Check Quote</label>
        <locationX>798</locationX>
        <locationY>708</locationY>
        <defaultConnectorLabel>Quote Not Found</defaultConnectorLabel>
        <rules>
            <name>Quote_Found</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>returnQuote</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Stash_quotes_with_same_domains</targetReference>
            </connector>
            <label>Quote Found</label>
        </rules>
    </decisions>
    <decisions>
        <name>checkFolderorSourceId</name>
        <label>checkFolderorSourceId</label>
        <locationX>1040</locationX>
        <locationY>384</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>SourceId</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.SourceQuoteId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Related_Source</targetReference>
            </connector>
            <label>SourceId</label>
        </rules>
    </decisions>
    <decisions>
        <name>CheckSkipTrigger</name>
        <label>CheckSkipTrigger</label>
        <locationX>1843</locationX>
        <locationY>384</locationY>
        <defaultConnector>
            <targetReference>GetAccAccRelAgency</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>SkipTrue</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>GetSkipTrigger.SkipInsurancePolicy__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <label>SkipTrue</label>
        </rules>
    </decisions>
    <decisions>
        <name>Is_Closed</name>
        <label>Is Closed</label>
        <locationX>754</locationX>
        <locationY>2796</locationY>
        <defaultConnector>
            <targetReference>ThereIsAProdOppNotClosed2</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Not Closed</defaultConnectorLabel>
        <rules>
            <name>Closed</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Loop_Through_Prodotto_Opportunities.StageName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Chiuso</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Add_id_to_list</targetReference>
            </connector>
            <label>Closed</label>
        </rules>
    </decisions>
    <decisions>
        <name>is_Previdenza</name>
        <label>is Previdenza</label>
        <locationX>545</locationX>
        <locationY>276</locationY>
        <defaultConnector>
            <targetReference>checkFolderorSourceId</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Previdenza</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>ESSIG_VITA_PREVIDENZA</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>handle_previdenza</targetReference>
            </connector>
            <label>Previdenza</label>
        </rules>
    </decisions>
    <description>vers prec 46
logica corretta del recupero quote finale</description>
    <environments>Default</environments>
    <formulas>
        <name>InsPolAgencyCode</name>
        <dataType>String</dataType>
        <expression>SUBSTITUTE({!$Record.Agency__r.ExternalId__c}, $Label.AgencyExtIdStr, &apos;&apos;)</expression>
    </formulas>
    <formulas>
        <name>InsPolCIPGrpName</name>
        <dataType>String</dataType>
        <expression>$Label.CIPExtIdStr + {!InsPolSocietyCode} + &apos;_&apos; + LPAD({!InsPolAgencyCode}, 5, &apos;0&apos;) + &apos;_&apos; + LPAD({!$Record.CIP__c}, 5, &apos;0&apos;)</expression>
    </formulas>
    <formulas>
        <name>InsPolSocietyCode</name>
        <dataType>String</dataType>
        <expression>SUBSTITUTE({!$Record.Society__r.ExternalId__c}, $Label.SocietyExtIdStr, &apos;&apos;)</expression>
    </formulas>
    <formulas>
        <name>OppAgeRefGroup</name>
        <dataType>String</dataType>
        <expression>{!$Label.RespGrpAgency} + {!InsPolAgencyCode}</expression>
    </formulas>
    <formulas>
        <name>taskSubject</name>
        <dataType>String</dataType>
        <expression>&apos;Emissione polizza: &apos; &amp; {!$Record.Name}</expression>
    </formulas>
    <interviewLabel>InsurancePolicyAfterInsWithConiAsync {!$Flow.CurrentDateTime}</interviewLabel>
    <label>InsurancePolicyAfterInsWithConiAsync</label>
    <loops>
        <name>Loop_Through_Active_Or_Sold_Quotes_Not_Stored</name>
        <label>Loop Through Active Or Sold Quotes Not Stored</label>
        <locationX>754</locationX>
        <locationY>3612</locationY>
        <collectionReference>All</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Sum_of_all_active_or_sold_quotes_not_stored</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Update_Premio_And_Ambito_On_Opp_In_gestione</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>Loop_Through_Prodotto_Opportunities</name>
        <label>Loop Through Prodotto Opportunities</label>
        <locationX>578</locationX>
        <locationY>2688</locationY>
        <collectionReference>Get_Prodotto_Opportunities</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Is_Closed</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Get_all_quotes_from_opp</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>Loop_Through_Sold_Quotes</name>
        <label>Loop Through Sold Quotes</label>
        <locationX>402</locationX>
        <locationY>3612</locationY>
        <collectionReference>All_Sold_quotes</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Sum_of_all_sold_quotes</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Close_Opportunity</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordCreates>
        <name>Create_Contact_History</name>
        <label>Create Contact History</label>
        <locationX>446</locationX>
        <locationY>1656</locationY>
        <connector>
            <targetReference>Get_all_quotes_from_opp_prod</targetReference>
        </connector>
        <inputAssignments>
            <field>InsurancePolicy__c</field>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Name</field>
            <value>
                <elementReference>taskSubject</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Opportunity__c</field>
            <value>
                <elementReference>Get_Quote_Opportunity.Parent__c</elementReference>
            </value>
        </inputAssignments>
        <object>ContactHistory__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>InsAccAccRelAgencyShareRec</name>
        <label>InsAccAccRelAgencyShareRec</label>
        <locationX>1942</locationX>
        <locationY>816</locationY>
        <connector>
            <targetReference>InsurancePolicyCreateSharingRule</targetReference>
        </connector>
        <inputAssignments>
            <field>AccessLevel</field>
            <value>
                <stringValue>Read</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>ParentId</field>
            <value>
                <elementReference>GetAccAccRelAgency.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>UserOrGroupId</field>
            <value>
                <elementReference>GetCIPGrp.Id</elementReference>
            </value>
        </inputAssignments>
        <object>FinServ__AccountAccountRelation__Share</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordLookups>
        <name>Get_all_quotes_from_opp</name>
        <label>Get all quotes from opp</label>
        <locationX>578</locationX>
        <locationY>3288</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>All_Prod_Opps_Closed</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>OpportunityId</field>
            <operator>In</operator>
            <value>
                <elementReference>prodIdsList</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Quote</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>QuoteAmount__c</queriedFields>
        <queriedFields>IsStored__c</queriedFields>
        <queriedFields>CommercialStatus__c</queriedFields>
        <queriedFields>AreasOfNeed__c</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_all_quotes_from_opp_prod</name>
        <label>Get all quotes from opp prod</label>
        <locationX>578</locationX>
        <locationY>1848</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Sold_quotes</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>OpportunityId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Quote_Opportunity.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>IsStored__c</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Quote</object>
        <queriedFields>Id</queriedFields>
        <queriedFields>CommercialStatus__c</queriedFields>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Prodotto_Opportunities</name>
        <label>Get Prodotto Opportunities</label>
        <locationX>578</locationX>
        <locationY>2580</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Loop_Through_Prodotto_Opportunities</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Parent__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Quote_Opportunity.Parent__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Opportunity</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Quote_Opportunity</name>
        <label>Get Quote Opportunity</label>
        <locationX>578</locationX>
        <locationY>1032</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Opp_Canale_di_Vendita</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>returnQuote.OpportunityId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Opportunity</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Related_Source</name>
        <label>Get Related Source</label>
        <locationX>798</locationX>
        <locationY>492</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assignment_Source</targetReference>
        </connector>
        <filterLogic>or</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.SourceQuoteId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Quote</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>GetAccAccRelAgency</name>
        <label>GetAccAccRelAgency</label>
        <locationX>2140</locationX>
        <locationY>492</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>GetCIPGrp</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>FinServ__Account__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.NameInsuredId</elementReference>
            </value>
        </filters>
        <filters>
            <field>FinServ__RelatedAccount__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.Agency__r.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>FinServ__AccountAccountRelation__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>GetCIPGrp</name>
        <label>GetCIPGrp</label>
        <locationX>2140</locationX>
        <locationY>600</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>check_AccAccRel_GroupCIP</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>InsPolCIPGrpName</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>GetSkipTrigger</name>
        <label>GetSkipTrigger</label>
        <locationX>1843</locationX>
        <locationY>276</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>CheckSkipTrigger</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>SetupOwnerId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$User.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>SkipTrigger__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Close_Opportunity</name>
        <label>Close Opportunity</label>
        <locationX>402</locationX>
        <locationY>4020</locationY>
        <connector>
            <targetReference>Update_Premio_And_Ambito_On_Opp_Chiuso</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Quote_Opportunity.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>AreaOfNeed__c</field>
            <value>
                <elementReference>AreasOfNeedOfSoldQuotes</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>ClosureSubstatus__c</field>
            <value>
                <stringValue>Polizza emessa</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>LossReason__c</field>
            <value>
                <stringValue></stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>PolicyChannel__c</field>
            <value>
                <elementReference>channel4Opp</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>StageName</field>
            <value>
                <stringValue>Chiuso</stringValue>
            </value>
        </inputAssignments>
        <object>Opportunity</object>
    </recordUpdates>
    <recordUpdates>
        <name>Close_Prodotto_Opportunity</name>
        <label>Close Prodotto Opportunity</label>
        <locationX>446</locationX>
        <locationY>2388</locationY>
        <connector>
            <targetReference>Get_Prodotto_Opportunities</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Quote_Opportunity.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>ClosureSubstatus__c</field>
            <value>
                <stringValue>Polizza emessa</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>LossReason__c</field>
            <value>
                <stringValue></stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>PolicyChannel__c</field>
            <value>
                <elementReference>channel4Opp</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>StageName</field>
            <value>
                <stringValue>Chiuso</stringValue>
            </value>
        </inputAssignments>
        <object>Opportunity</object>
    </recordUpdates>
    <recordUpdates>
        <name>Relate_Quote_to_Policy</name>
        <label>Relate Quote to Policy</label>
        <locationX>578</locationX>
        <locationY>1440</locationY>
        <connector>
            <targetReference>Check_Ins_Policy_RecordType</targetReference>
        </connector>
        <inputAssignments>
            <field>SourceOpportunityId</field>
            <value>
                <elementReference>Get_Quote_Opportunity.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>SourceQuoteId</field>
            <value>
                <elementReference>returnQuote.Id</elementReference>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Retrieve_Policy_Channel_for_Container</name>
        <label>Retrieve Policy Channel for Container</label>
        <locationX>710</locationX>
        <locationY>2388</locationY>
        <connector>
            <targetReference>Get_Prodotto_Opportunities</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Quote_Opportunity.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>PolicyChannel__c</field>
            <value>
                <elementReference>channel4Opp</elementReference>
            </value>
        </inputAssignments>
        <object>Opportunity</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Premio_And_Ambito_On_Opp_Chiuso</name>
        <label>Update Premio And Ambito On Opp Chiuso</label>
        <locationX>402</locationX>
        <locationY>4128</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Quote_Opportunity.Parent__c</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Amount</field>
            <value>
                <elementReference>SumOfAllSoldQuotes</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>AreaOfNeed__c</field>
            <value>
                <elementReference>AreasOfNeedOfSoldQuotes</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>PolicyChannel__c</field>
            <value>
                <elementReference>channel4Opp</elementReference>
            </value>
        </inputAssignments>
        <object>Opportunity</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Premio_And_Ambito_On_Opp_In_gestione</name>
        <label>Update Premio And Ambito On Opp In gestione</label>
        <locationX>754</locationX>
        <locationY>4020</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Quote_Opportunity.Parent__c</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>Amount</field>
            <value>
                <elementReference>SumOfActiveOrSoldQuotesNotStored</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>AreaOfNeed__c</field>
            <value>
                <elementReference>AreasOfNeedOfActiveOrSoldQuotesNotStored</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>PolicyChannel__c</field>
            <value>
                <elementReference>channel4Opp</elementReference>
            </value>
        </inputAssignments>
        <object>Opportunity</object>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Quote_Status</name>
        <label>Update Quote Status</label>
        <locationX>578</locationX>
        <locationY>924</locationY>
        <connector>
            <targetReference>Get_Quote_Opportunity</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>returnQuote.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>CommercialStatus__c</field>
            <value>
                <stringValue>SOLD</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>IsStored__c</field>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>PolicyChannel__c</field>
            <value>
                <elementReference>$Record.Channel__c</elementReference>
            </value>
        </inputAssignments>
        <object>Quote</object>
    </recordUpdates>
    <start>
        <locationX>1068</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>is_Previdenza</targetReference>
        </connector>
        <object>InsurancePolicy</object>
        <recordTriggerType>Create</recordTriggerType>
        <scheduledPaths>
            <name>OppInsPolShares</name>
            <connector>
                <targetReference>GetSkipTrigger</targetReference>
            </connector>
            <label>OppInsPolShares</label>
            <offsetNumber>0</offsetNumber>
            <offsetUnit>Minutes</offsetUnit>
            <timeSource>RecordTriggerEvent</timeSource>
        </scheduledPaths>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
    <subflows>
        <name>handle_previdenza</name>
        <label>handle previdenza</label>
        <locationX>50</locationX>
        <locationY>384</locationY>
        <flowName>InsurancePolicyAfterInsWithConiAsync_Previdenza</flowName>
        <inputAssignments>
            <name>insurancePolicyInput</name>
            <value>
                <elementReference>$Record</elementReference>
            </value>
        </inputAssignments>
    </subflows>
    <subflows>
        <name>Stash_quotes_with_same_domains</name>
        <label>Stash quotes with same domains</label>
        <locationX>578</locationX>
        <locationY>816</locationY>
        <connector>
            <targetReference>Update_Quote_Status</targetReference>
        </connector>
        <flowName>Active_Quote_Management</flowName>
        <inputAssignments>
            <name>targetQuote</name>
            <value>
                <elementReference>returnQuote</elementReference>
            </value>
        </inputAssignments>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </subflows>
    <variables>
        <name>AreasOfNeedOfActiveOrSoldQuotesNotStored</name>
        <dataType>Multipicklist</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>AreasOfNeedOfSoldQuotes</name>
        <dataType>Multipicklist</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>channel4Opp</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>currentItem_All</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Quote</objectType>
    </variables>
    <variables>
        <name>currentItem_All_Sold_quotes</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Quote</objectType>
    </variables>
    <variables>
        <name>currentItemFromSourceCollection</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Quote</objectType>
    </variables>
    <variables>
        <name>DomainOfStoredQuotes</name>
        <dataType>Multipicklist</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>NotStoredQuotes</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Quote</objectType>
    </variables>
    <variables>
        <name>NumberOfSoldQuotes</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
    </variables>
    <variables>
        <name>NumberOfTotalQuotes</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
    </variables>
    <variables>
        <name>OppAgencyRefGrp</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>OppRefShareRecToUpd</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
        <objectType>OpportunityShare</objectType>
    </variables>
    <variables>
        <name>OppShareRecToUpd</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
        <objectType>OpportunityShare</objectType>
    </variables>
    <variables>
        <name>prodIdsList</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>QuoteswithInsuredAsset</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Quote</objectType>
    </variables>
    <variables>
        <name>returnQuote</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Quote</objectType>
    </variables>
    <variables>
        <name>StoredQuotesToUpdate</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Quote</objectType>
    </variables>
    <variables>
        <name>StoredQuotesToUpdate2</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Quote</objectType>
    </variables>
    <variables>
        <name>SumOfActiveOrSoldQuotesNotStored</name>
        <dataType>Currency</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>2</scale>
    </variables>
    <variables>
        <name>SumOfAllSoldQuotes</name>
        <dataType>Currency</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>2</scale>
    </variables>
    <variables>
        <name>ThereIsANonSoldQuote</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>ThereIsAProdOppNotClosed</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>UpdateQuotesWithInsuredAssetToStored</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Quote</objectType>
    </variables>
    <variables>
        <name>UpdateQuotesWithInsuredAssetToStoredNoDuplicates</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Quote</objectType>
    </variables>
</Flow>
