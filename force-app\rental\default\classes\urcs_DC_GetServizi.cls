/**
 * @File Name         : urcs_DC_GetServizi.cls
 * @Description       : 
 * <AUTHOR> VE
 * @Group             : 
 * @Last Modified On  : 11-07-2025
 * @Last Modified By  : VE
**/
global with sharing class urcs_DC_GetServizi implements System.Callable {

    public Object call(String action, Map<String,Object> args){
        Map<String,Object> input = (Map<String,Object>) args.get('input');
        Map<String,Object> output = (Map<String,Object>) args.get('output');
        Map<String,Object> options = (Map<String,Object>) args.get('options');
        invokeMethod(input, output, options);
        return null;
    }
        
    @AuraEnabled
    global static Map<String,Object> invokeMethod(Map<String,Object> input,Map<String,Object> output, Map<String,Object> options){
        
        system.debug('input '+ JSON.serialize(input));
        system.debug('output '+ JSON.serialize(output));
        system.debug('options '+ JSON.serialize(options));
        Map<String,Object> setValueMap = (Map<String,Object>) input.get('Set Values');
        String method = (String) setValueMap.get('methodExecute');
        try{
            if(method.equalsIgnoreCase('init')){
                output = getServizi(input,output,options);
            }
        }catch(Exception e){
            output.put('error',e.getMessage() + ' ' + e.getStackTraceString());
        }
        return output;
    }
    
    public static Map<String,Object> getServizi(Map<String,Object> input,Map<String,Object> output, Map<String,Object> options){
        try{
            String idContratto =  null != (String)input.get('recordId') ? (String)input.get('recordId') : null;

            ServiceContract serviceContract = [SELECT id, name FROM ServiceContract WHERE id=: idContratto LIMIT 1];
            Integer name = Integer.valueOf(serviceContract.name);
            String queryString = 'SELECT ID_CONTRATTO__c, '+    
                                    'ID_SERVIZIO__c, '+
                                    'DS_TIPOLOGIA_SERVIZIO__c, '+
                                    'DS_SERVIZIO__c, '+
                                    'FL_INCLUSIONE__c, '+
                                    'DS_DETTAGLIO_INCLUSIONE__c ' +
                                    'FROM UR_SERVIZI__dlm '  +
                                    'WHERE ID_CONTRATTO__c =: name ';
            List<sObject> listServizi = Database.query(queryString);

            output.put('data',listServizi);

        } catch (Exception e){

            output.put('data',null);
            System.debug('error in urcs_DC_GetServizi method invokeMethod: '+ json.serializePretty(e));
        }

        return output;
    }



}