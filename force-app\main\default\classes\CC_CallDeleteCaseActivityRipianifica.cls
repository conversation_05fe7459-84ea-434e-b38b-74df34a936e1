/**
 * @description       : 
 * <AUTHOR> <EMAIL>
 * @group             : 
 * @last modified on  : 09-12-2025
 * @last modified by  : <EMAIL>
**/
public with sharing class CC_CallDeleteCaseActivityRipianifica {

    public class Request {
        @InvocableVariable(required=true)
        public String campaign;

        @InvocableVariable(required=true)
        public String listId;

        @InvocableVariable(required=true)
        public String phone;
    }

    public class Response {
        @InvocableVariable
        public Boolean success;

        @InvocableVariable
        public String message;

        @InvocableVariable
        public String statusCode;
    }

    @InvocableMethod(
        label='Remove Contact from COMET'
        description='Invoca CometIntegration.removeContact per rimuovere un contatto da una campagna'
    )
    public static List<Response> removeContactFromFlow(List<Request> requests) {
        List<Response> results = new List<Response>();
        for (Request req : requests) {
            Response res = new Response();
            try {
                res.statusCode = '400';
                if (String.isBlank(req.campaign)) {
                    res.success = false;
                    res.message = 'Campo campaign vuoto';
                } else if (String.isBlank(req.listId)) {
                    res.success = false;
                    res.message = 'Campo listId vuoto';
                } else if (String.isBlank(req.phone) || !req.phone.startsWith('+39')) {
                    res.success = false;
                    res.message = 'Numero non valido: deve iniziare con +39';
                } else {
                    CometIntegrationTypes.RemoveContactResponse cometResponse =
                        CometIntegration.removeContact(req.campaign, req.listId, req.phone);
                    System.debug('CometResponse : '+ cometResponse);
                    res.success = true; // Inserire poi il ritorno dal ip
                    res.statusCode = '200';  // Inserire poi lo status code reale del IP
                    res.message = 'Contatto rimosso'; // Ilserire il messaggio di errore dal ip
                }
            } catch (Exception ex) {
                res.success = false;
                res.statusCode = '400';
                res.message = 'Errore: ' + ex.getMessage();
            }

            results.add(res);
        }

        return results;
    }
}