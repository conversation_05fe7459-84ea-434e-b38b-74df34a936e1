<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>64.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>collectorIdMilestone</name>
        <label>collectorIdMilestone</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>listMilestoneToComplete</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>LoopMilestone</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>LoopMilestone</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Copy_1_of_statusCase</name>
        <label>statusCase</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>getCase.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Chiuso - <PERSON>ullato</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>getCase.NoteChiusuraAgente__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Note_chiusura_agente</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>getCase.NoteChiusura__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Note_chiusura</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>getMilestone</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Copy_3_of_statusCase</name>
        <label>statusCase</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>getCase.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Chiuso - Annullato</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>getCase.NoteChiusura__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Note_chiusura</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>getCase.NoteChiusuraCliente__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Note_chiusura_cliente</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>getMilestone</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>noteChiusura</name>
        <label>noteChiusura</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>getCase.NoteChiusura__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Note_chiusura</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>getCase.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>tipoChiusura</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>getMilestone</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>noteChiusuraAgente</name>
        <label>noteChiusuraAgente</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>getCase.NoteChiusuraAgente__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Note_chiusura_agente</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>getCase.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>tipoChiusura</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>getCase.NoteChiusura__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Note_chiusura</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>getMilestone</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>noteChiusuraAgenteLimited</name>
        <label>noteChiusuraAgenteLimited</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>getCase.NoteChiusuraAgente__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Note_chiusura_agente_Limited</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>getCase.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>tipoChiusuraLimited</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>getCase.NoteChiusura__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Note_chiusura_Limited</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>getMilestone</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>noteChiusuraCliente</name>
        <label>noteChiusuraCliente</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>getCase.NoteChiusuraCliente__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Note_chiusura_cliente</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>getCase.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>tipoChiusura</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>getCase.NoteChiusura__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Note_chiusura</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>getMilestone</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>noteChiusuraClienteLimited</name>
        <label>noteChiusuraClienteLimited</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>getCase.NoteChiusuraCliente__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Note_chiusura_cliente_Limited</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>getCase.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>tipoChiusuraLimited</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>getCase.NoteChiusura__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Note_chiusura_Limited</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>getMilestone</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>noteChiusuraLimited</name>
        <label>noteChiusuraLimited</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>getCase.NoteChiusura__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Note_chiusura_Limited</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>getCase.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>tipoChiusuraLimited</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>getMilestone</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>setMilestoneCompleted</name>
        <label>setMilestoneCompleted</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>LoopMilestone.CompletionDate</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>oggi</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>collectorIdMilestone</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>statusCase</name>
        <label>statusCase</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>getCase.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Chiuso - Annullato</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>getCase.NoteChiusura__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Note_chiusura</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>getMilestone</targetReference>
        </connector>
    </assignments>
    <choices>
        <name>ChiusoAnnullato</name>
        <choiceText>Chiuso - Annullato</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Chiuso - Annullato</stringValue>
        </value>
    </choices>
    <choices>
        <name>ChiusoRisolto</name>
        <choiceText>Chiuso - Risolto</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Chiuso - Risolto</stringValue>
        </value>
    </choices>
    <choices>
        <name>ChiusoRisoltoOnly</name>
        <choiceText>Chiuso - Risolto</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Chiuso - Risolto</stringValue>
        </value>
    </choices>
    <customProperties>
        <name>ScreenProgressIndicator</name>
        <value>
            <stringValue>{&quot;location&quot;:&quot;top&quot;,&quot;type&quot;:&quot;simple&quot;}</stringValue>
        </value>
    </customProperties>
    <decisions>
        <name>check_status</name>
        <label>check status</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Messaggio_errore_status</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>in_Gestione</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>getCase.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>In gestione</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>getCase.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Nuova richiesta</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Chiusura_Case_Limited</targetReference>
            </connector>
            <label>in Gestione</label>
        </rules>
    </decisions>
    <decisions>
        <name>checkHasMilestone</name>
        <label>checkHasMilestone</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Close_Case</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>chiudiMilestone</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>getMilestone</leftValueReference>
                <operator>IsEmpty</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>LoopMilestone</targetReference>
            </connector>
            <label>chiudiMilestone</label>
        </rules>
    </decisions>
    <decisions>
        <name>checkPermissionAnnullaCase</name>
        <label>checkPermissionAnnullaCase</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>check_status</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>No Permission</defaultConnectorLabel>
        <rules>
            <name>hasAnnullaCasePermission</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>hasPermissionAnnullaCase</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Chiusura_Case</targetReference>
            </connector>
            <label>Has Permission</label>
        </rules>
    </decisions>
    <decisions>
        <name>checkUtenteAssegnatario</name>
        <label>checkUtenteAssegnatario</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>checkPermissionAnnullaCase</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>si coda</defaultConnectorLabel>
        <rules>
            <name>no_coda</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>getCase.UtAssegnatario__c</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <elementReference>CurrentUserId</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>ErroreOwner</targetReference>
            </connector>
            <label>no coda</label>
        </rules>
    </decisions>
    <decisions>
        <name>chiusoAnnullatoTerzeParti</name>
        <label>chiusoAnnullatoTerzeParti</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>noteChiusura</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>terze_parti</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>getCase.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>In attesa terze parti</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>getCase.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Ricevuta risposta cliente</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>getCase.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Trasferito</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>getCase.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Mancata risposta cliente</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>statusCase</targetReference>
            </connector>
            <label>terze parti</label>
        </rules>
    </decisions>
    <decisions>
        <name>chiusoAnnullatoTerzePartiAR</name>
        <label>chiusoAnnullatoTerzePartiAR</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>noteChiusuraCliente</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Copy_3_of_terze_parti</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>getCase.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>In attesa terze parti</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>getCase.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Ricevuta risposta cliente</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>getCase.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Trasferito</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>getCase.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Mancata risposta cliente</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_3_of_statusCase</targetReference>
            </connector>
            <label>terze parti</label>
        </rules>
    </decisions>
    <decisions>
        <name>chiusoAnnullatoTerzePartiPQ</name>
        <label>chiusoAnnullatoTerzePartiPQ</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>noteChiusuraAgente</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Copy_1_of_terze_parti</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>getCase.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>In attesa terze parti</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>getCase.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Ricevuta risposta cliente</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>getCase.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Trasferito</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>getCase.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Mancata risposta cliente</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_1_of_statusCase</targetReference>
            </connector>
            <label>terze parti</label>
        </rules>
    </decisions>
    <decisions>
        <name>updateCaseByRT</name>
        <label>updateCaseByRT</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>getMilestone</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>CaseCRMSitoWeb</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>getCase.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>ur_CaseCRM</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>getCase.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>ur_CaseSitoWeb</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>getCase.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>ur_CaseES</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>chiusoAnnullatoTerzeParti</targetReference>
            </connector>
            <label>CaseCRMSitoWeb</label>
        </rules>
        <rules>
            <name>CasePQ</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>getCase.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>ur_CasePQ</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>chiusoAnnullatoTerzePartiPQ</targetReference>
            </connector>
            <label>CasePQ</label>
        </rules>
        <rules>
            <name>CaseAR</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>getCase.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>ur_CaseAR</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>chiusoAnnullatoTerzePartiAR</targetReference>
            </connector>
            <label>CaseAR</label>
        </rules>
    </decisions>
    <decisions>
        <name>updateCaseByRTLimited</name>
        <label>updateCaseByRTLimited</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>getMilestone</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>default</defaultConnectorLabel>
        <rules>
            <name>CaseCRMSitoWebLimited</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>getCase.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>ur_CaseCRM</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>getCase.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>ur_CaseSitoWeb</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>getCase.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>ur_CaseES</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>noteChiusuraLimited</targetReference>
            </connector>
            <label>CaseCRMSitoWebLimited</label>
        </rules>
        <rules>
            <name>CasePQLimited</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>getCase.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>ur_CasePQ</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>noteChiusuraAgenteLimited</targetReference>
            </connector>
            <label>CasePQLimited</label>
        </rules>
        <rules>
            <name>CaseARLimited</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>getCase.RecordType.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>ur_CaseAR</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>noteChiusuraClienteLimited</targetReference>
            </connector>
            <label>CaseARLimited</label>
        </rules>
    </decisions>
    <description>Flow per chiusura case con note visible e chiusura milestone fix, aggiunto controllo stati per operatore senza PS annulla case</description>
    <environments>Default</environments>
    <formulas>
        <name>CurrentUserId</name>
        <dataType>String</dataType>
        <expression>CASESAFEID({!$User.Id})</expression>
    </formulas>
    <formulas>
        <name>hasPermissionAnnullaCase</name>
        <dataType>Boolean</dataType>
        <expression>{!$Permission.urcs_AnnullaCase}</expression>
    </formulas>
    <formulas>
        <name>oggi</name>
        <dataType>DateTime</dataType>
        <expression>NOW()</expression>
    </formulas>
    <interviewLabel>urcs_Case {!$Flow.CurrentDateTime}</interviewLabel>
    <label>urcs_CaseChiusura</label>
    <loops>
        <name>LoopMilestone</name>
        <label>LoopMilestone</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <collectionReference>getMilestone</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>setMilestoneCompleted</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>completeMilestone</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>getCase</name>
        <label>getCase</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>checkUtenteAssegnatario</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>ScreenErrorMsg</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getMilestone</name>
        <label>getMilestone</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>checkHasMilestone</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>ScreenErrorMsg</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>CaseId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>getCase.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>CaseMilestone</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Close_Case</name>
        <label>Close Case</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <faultConnector>
            <targetReference>ScreenErrorMsg</targetReference>
        </faultConnector>
        <inputReference>getCase</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>completeMilestone</name>
        <label>completeMilestone</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Close_Case</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>ScreenErrorMsg</targetReference>
        </faultConnector>
        <inputReference>listMilestoneToComplete</inputReference>
    </recordUpdates>
    <screens>
        <name>Chiusura_Case</name>
        <label>Chiusura Case</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>updateCaseByRT</targetReference>
        </connector>
        <fields>
            <name>tipoChiusuraTerzeParti</name>
            <choiceReferences>ChiusoAnnullato</choiceReferences>
            <dataType>String</dataType>
            <defaultSelectedChoiceReference>ChiusoAnnullato</defaultSelectedChoiceReference>
            <fieldText>Tipologia Chiusura</fieldText>
            <fieldType>DropdownBox</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isDisabled>
                <booleanValue>true</booleanValue>
            </isDisabled>
            <isRequired>true</isRequired>
            <visibilityRule>
                <conditionLogic>or</conditionLogic>
                <conditions>
                    <leftValueReference>getCase.Status</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <stringValue>In attesa terze parti</stringValue>
                    </rightValue>
                </conditions>
                <conditions>
                    <leftValueReference>getCase.Status</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <stringValue>Ricevuta risposta cliente</stringValue>
                    </rightValue>
                </conditions>
                <conditions>
                    <leftValueReference>getCase.Status</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <stringValue>Trasferito</stringValue>
                    </rightValue>
                </conditions>
                <conditions>
                    <leftValueReference>getCase.Status</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <stringValue>Mancata risposta cliente</stringValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>tipoChiusura</name>
            <choiceReferences>ChiusoRisolto</choiceReferences>
            <choiceReferences>ChiusoAnnullato</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Tipologia Chiusura</fieldText>
            <fieldType>DropdownBox</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isDisabled>
                <booleanValue>false</booleanValue>
            </isDisabled>
            <isRequired>true</isRequired>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>getCase.Status</leftValueReference>
                    <operator>NotEqualTo</operator>
                    <rightValue>
                        <stringValue>In attesa terze parti</stringValue>
                    </rightValue>
                </conditions>
                <conditions>
                    <leftValueReference>getCase.Status</leftValueReference>
                    <operator>NotEqualTo</operator>
                    <rightValue>
                        <stringValue>Ricevuta risposta cliente</stringValue>
                    </rightValue>
                </conditions>
                <conditions>
                    <leftValueReference>getCase.Status</leftValueReference>
                    <operator>NotEqualTo</operator>
                    <rightValue>
                        <stringValue>Trasferito</stringValue>
                    </rightValue>
                </conditions>
                <conditions>
                    <leftValueReference>getCase.Status</leftValueReference>
                    <operator>NotEqualTo</operator>
                    <rightValue>
                        <stringValue>Mancata risposta cliente</stringValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>Note_chiusura</name>
            <fieldText>Note chiusura</fieldText>
            <fieldType>LargeTextArea</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <visibilityRule>
                <conditionLogic>or</conditionLogic>
                <conditions>
                    <leftValueReference>getCase.RecordType.DeveloperName</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <stringValue>ur_CaseCRM</stringValue>
                    </rightValue>
                </conditions>
                <conditions>
                    <leftValueReference>getCase.RecordType.DeveloperName</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <stringValue>ur_CaseSitoWeb</stringValue>
                    </rightValue>
                </conditions>
                <conditions>
                    <leftValueReference>getCase.RecordType.DeveloperName</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <stringValue>ur_CaseAR</stringValue>
                    </rightValue>
                </conditions>
                <conditions>
                    <leftValueReference>getCase.RecordType.DeveloperName</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <stringValue>ur_CasePQ</stringValue>
                    </rightValue>
                </conditions>
                <conditions>
                    <leftValueReference>getCase.RecordType.DeveloperName</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <stringValue>ur_CaseES</stringValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>Note_chiusura_agente</name>
            <fieldText>Note chiusura agente</fieldText>
            <fieldType>LargeTextArea</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>getCase.RecordType.DeveloperName</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <stringValue>ur_CasePQ</stringValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>Note_chiusura_cliente</name>
            <fieldText>Note chiusura cliente</fieldText>
            <fieldType>LargeTextArea</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>getCase.RecordType.DeveloperName</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <stringValue>ur_CaseAR</stringValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <nextOrFinishButtonLabel>Avanti</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Chiusura_Case_Limited</name>
        <label>Chiusura Case (Limitato)</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>updateCaseByRTLimited</targetReference>
        </connector>
        <fields>
            <name>tipoChiusuraLimited</name>
            <choiceReferences>ChiusoRisoltoOnly</choiceReferences>
            <dataType>String</dataType>
            <defaultSelectedChoiceReference>ChiusoRisoltoOnly</defaultSelectedChoiceReference>
            <fieldText>Tipologia Chiusura</fieldText>
            <fieldType>DropdownBox</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isDisabled>
                <booleanValue>true</booleanValue>
            </isDisabled>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>Note_chiusura_Limited</name>
            <fieldText>Note chiusura</fieldText>
            <fieldType>LargeTextArea</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <visibilityRule>
                <conditionLogic>or</conditionLogic>
                <conditions>
                    <leftValueReference>getCase.RecordType.DeveloperName</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <stringValue>ur_CaseCRM</stringValue>
                    </rightValue>
                </conditions>
                <conditions>
                    <leftValueReference>getCase.RecordType.DeveloperName</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <stringValue>ur_CaseSitoWeb</stringValue>
                    </rightValue>
                </conditions>
                <conditions>
                    <leftValueReference>getCase.RecordType.DeveloperName</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <stringValue>ur_CaseAR</stringValue>
                    </rightValue>
                </conditions>
                <conditions>
                    <leftValueReference>getCase.RecordType.DeveloperName</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <stringValue>ur_CasePQ</stringValue>
                    </rightValue>
                </conditions>
                <conditions>
                    <leftValueReference>getCase.RecordType.DeveloperName</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <stringValue>uc_CaseES</stringValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>Note_chiusura_agente_Limited</name>
            <fieldText>Note chiusura agente</fieldText>
            <fieldType>LargeTextArea</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>getCase.RecordType.DeveloperName</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <stringValue>ur_CasePQ</stringValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>Note_chiusura_cliente_Limited</name>
            <fieldText>Note chiusura cliente</fieldText>
            <fieldType>LargeTextArea</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>getCase.RecordType.DeveloperName</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <stringValue>ur_CaseAR</stringValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <nextOrFinishButtonLabel>Avanti</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>ErroreOwner</name>
        <label>ErroreOwner</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>errorMessage</name>
            <fieldText>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255);&quot;&gt;Per effettuare l&apos;operazione è necessario prendere in carico il case.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <nextOrFinishButtonLabel>Esci</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Messaggio_errore_status</name>
        <label>Messaggio errore status</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>messaggio_errore_text</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;Non è possibile effettuare questa operazione &lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>ScreenErrorMsg</name>
        <label>ScreenErrorMsg</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>TextErrorMsg</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;Si è verificato un errore: impossibile completare l&apos;operazione.&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(255, 0, 0);&quot;&gt;{!$Flow.FaultMessage}&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>getCase</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>listIdMilestoneToUpdate</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>listMilestoneToComplete</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>CaseMilestone</objectType>
    </variables>
    <variables>
        <description>id case</description>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
</Flow>
