<?xml version="1.0" encoding="UTF-8"?>
<OmniIntegrationProcedure xmlns="http://soap.sforce.com/2006/04/metadata">
    <customJavaScript>{
    &quot;accountId&quot;: &quot;0019X000015e2ZLQAY&quot;,
    &quot;documentId&quot;: &quot;0699O00000D9gi5QAB&quot;,
    &quot;externalId&quot;: &quot;SOC_1&quot;,
    &quot;userId&quot;: &quot;0059X00000Jr08cQAB&quot;,
    &quot;codiceBelfioreComune&quot;: &quot;H224&quot;,
    &quot;codiceBelfioreNazione&quot;: &quot;Z000&quot;,
    &quot;codiceProvincia&quot;: &quot;RC&quot;,
    &quot;dataRilascio&quot;: &quot;2025-06-01&quot;,
    &quot;dataScadenza&quot;: &quot;2025-09-04&quot;,
    &quot;numeroDocumento&quot;: &quot;800015888111112588&quot;,
    &quot;tipoDelDocumento&quot;: &quot;TSS&quot;,
    &quot;Ciu&quot;: &quot;1719&quot;,
    &quot;accountDetailsId&quot;: &quot;a1i9X000006E9mWQAS&quot;,
    &quot;tipoEnte&quot;: &quot;012&quot;,
    &quot;omniScriptId&quot;: &quot;0jN9O000003YHuLUAW&quot;,
    &quot;elementName&quot;: &quot;Upload&quot;
}</customJavaScript>
    <description>@LS: Troncato a 15 num doc.</description>
    <elementTypeComponentMapping>{&quot;ElementTypeToHTMLTemplateList&quot;:[]}</elementTypeComponentMapping>
    <isActive>true</isActive>
    <isIntegProcdSignatureAvl>false</isIntegProcdSignatureAvl>
    <isIntegrationProcedure>true</isIntegrationProcedure>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <isMetadataCacheDisabled>false</isMetadataCacheDisabled>
    <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
    <isTestProcedure>false</isTestProcedure>
    <isWebCompEnabled>false</isWebCompEnabled>
    <language>Procedure</language>
    <name>UniDocumentale_Creazione</name>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>DeleteDocument</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;remoteOptions&quot; : { },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;disableChainable&quot; : false,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;integrationProcedureKey&quot; : &quot;UniDS_DeleteOmniscriptDocument&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;documentId&quot; : &quot;%documentId%&quot;
  },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
        <sequenceNumber>9.0</sequenceNumber>
        <type>Integration Procedure Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>Deserialize</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;request&quot; : &quot;%IntegrationServiceApex:request%&quot;,
    &quot;endPoint&quot; : &quot;%IntegrationServiceApex:endPoint%&quot;,
    &quot;response&quot; : &quot;=%IntegrationServiceApex:response%&quot;,
    &quot;body&quot; : &quot;=DESERIALIZE(%IntegrationServiceApex:response:body%)&quot;,
    &quot;statusCode&quot; : &quot;=%IntegrationServiceApex:response:statusCode%&quot;,
    &quot;status&quot; : &quot;=%IntegrationServiceApex:response:status%&quot;
  },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
        <sequenceNumber>7.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>ExtractUserInfos</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;dataRaptor Input Parameters&quot; : [ {
    &quot;inputParam&quot; : &quot;AccountDetailsId&quot;,
    &quot;element&quot; : &quot;accountDetailsId&quot;
  }, {
    &quot;inputParam&quot; : &quot;UserId&quot;,
    &quot;element&quot; : &quot;userId&quot;
  } ],
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;bundle&quot; : &quot;UniGetNetworkUserInfo&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
        <sequenceNumber>2.0</sequenceNumber>
        <type>DataRaptor Extract Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>GenericLog</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;remoteOptions&quot; : { },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;remoteMethod&quot; : &quot;call&quot;,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;remoteClass&quot; : &quot;UniLogIntegrationProcedureSubmit&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;additionalChainableResponse&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;Message&quot; : &quot;=&apos;Documentale Creazione&apos;&quot;,
    &quot;ClassName&quot; : &quot;=&apos;uniUtils&apos;&quot;,
    &quot;Payload&quot; : &quot;%Deserialize:body%&quot;,
    &quot;MethodName&quot; : &quot;=&apos;uploadDocument&apos;&quot;
  },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
        <sequenceNumber>8.0</sequenceNumber>
        <type>Remote Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>false</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>getCodeEnte</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;remoteOptions&quot; : { },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : false,
  &quot;disableChainable&quot; : false,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;integrationProcedureKey&quot; : &quot;UniDS_CodiceEnteEmissione&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;tipoEnte&quot; : &quot;%tipoEnte%&quot;,
    &quot;status&quot; : true
  },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
        <sequenceNumber>1.0</sequenceNumber>
        <type>Integration Procedure Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <description>rimosso il tag &quot;file&quot; che viene aggiunto nell&apos;apex, old version --&gt; file:%GetDocInfo:VersionData%</description>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SetFinalBody</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;bodyFinal&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;codiceBelfioreComune&quot; : &quot;%codiceBelfioreComune%&quot;,
    &quot;codiceBelfioreNazione&quot; : &quot;%codiceBelfioreNazione%&quot;,
    &quot;dataRilascio&quot; : &quot;=IF(ISBLANK(%dataRilascio%), null, %dataRilascio%)&quot;,
    &quot;statoDelDocumento&quot; : &quot;V&quot;,
    &quot;userId&quot; : &quot;%ExtractUserInfos:userId%&quot;,
    &quot;codiceFiscale&quot; : &quot;=QUERY(\&quot;SELECT ExternalId__c from Account where Id = &apos;{0}&apos;\&quot;, %accountId%)&quot;,
    &quot;ciu&quot; : &quot;%Ciu%&quot;,
    &quot;dataScadenza&quot; : &quot;=IF(ISBLANK(%dataScadenza%), null, %dataScadenza%)&quot;,
    &quot;enteRilascio&quot; : &quot;%tipoEnte%&quot;,
    &quot;compagnia&quot; : &quot;=IF(%externalId% == \&quot;SOC_1\&quot;, \&quot;unipolsai\&quot;,\&quot;unisalute\&quot;)&quot;,
    &quot;codiceProvincia&quot; : &quot;%codiceProvincia%&quot;,
    &quot;codiceFilenet&quot; : &quot;=&quot;,
    &quot;profiled&quot; : false,
    &quot;tipoDelDocumento&quot; : &quot;%tipoDelDocumento%&quot;,
    &quot;numeroDocumento&quot; : &quot;=SUBSTRING(%numeroDocumento%,0,15)&quot;,
    &quot;username&quot; : &quot;%ExtractUserInfos:username%&quot;
  },
  &quot;disOnTplt&quot; : false,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;id&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
        <sequenceNumber>5.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SetParamsQuery</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;params:Params:QueryParam&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : { },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
        <sequenceNumber>4.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SetParamsUrl</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;params:Params:PathParam&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : { },
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
        <sequenceNumber>3.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>IntegrationServiceApex</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;remoteOptions&quot; : { },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;remoteMethod&quot; : &quot;uploadDocument&quot;,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;remoteClass&quot; : &quot;uniUtils&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;additionalChainableResponse&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;integrationId&quot; : &quot;=\&quot;PostDocumentale_Creazione\&quot;&quot;,
    &quot;documentId&quot; : &quot;%documentId%&quot;,
    &quot;bodyFinal&quot; : &quot;%bodyFinal%&quot;
  },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Remote Action</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>TryCatchBlock1</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;failOnBlockError&quot; : true,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;remoteClass&quot; : &quot;&quot;,
  &quot;remoteMethod&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : {
    &quot;IntegrationServiceStatus&quot; : &quot;KO&quot;
  },
  &quot;isActive&quot; : true
}</propertySetConfig>
        <sequenceNumber>6.0</sequenceNumber>
        <type>Try Catch Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>checkError</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;remoteOptions&quot; : { },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;remoteMethod&quot; : &quot;parseResponseJson&quot;,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;remoteClass&quot; : &quot;uniUtils&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;additionalChainableResponse&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;response&quot; : &quot;%IntegrationServiceApex:response:body%&quot;
  },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Remote Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ResponseActionKO</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;vlcResponseHeaders&quot; : { },
  &quot;returnFullDataJSON&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : true,
  &quot;show&quot; : null,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;isActive&quot; : true,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : {
    &quot;IntegrationServiceStatus&quot; : &quot;=&apos;KO&apos;&quot;,
    &quot;fullRequest:body&quot; : &quot;%Deserialize:request%&quot;,
    &quot;fullResponse&quot; : &quot;%Deserialize:body%&quot;,
    &quot;checkError&quot; : &quot;%checkError:isAntiLaundering%&quot;,
    &quot;message&quot; : &quot;%Deserialize:body:error:errorMessage%&quot;,
    &quot;fullRequest:endPoint&quot; : &quot;%Deserialize:endPoint%&quot;,
    &quot;statusCode&quot; : &quot;%Deserialize:statusCode%&quot;,
    &quot;status&quot; : &quot;%Deserialize:status%&quot;
  },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;useFormulas&quot; : true,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>Response Action</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>Upload_Error</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%Deserialize:statusCode% != 201&quot;,
  &quot;show&quot; : null,
  &quot;disOnTplt&quot; : false,
  &quot;isActive&quot; : true,
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
        <sequenceNumber>11.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ResponseActionOK</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;vlcResponseHeaders&quot; : { },
  &quot;returnFullDataJSON&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : true,
  &quot;show&quot; : null,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;isActive&quot; : true,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : {
    &quot;IntegrationServiceStatus&quot; : &quot;=&apos;OK&apos;&quot;,
    &quot;fullRequest:body&quot; : &quot;%Deserialize:request%&quot;,
    &quot;fullResponse&quot; : &quot;%Deserialize:body%&quot;,
    &quot;message&quot; : &quot;&apos;The process completed successfully.&apos;&quot;,
    &quot;fullRequest:endPoint&quot; : &quot;%Deserialize:endPoint%&quot;,
    &quot;statusCode&quot; : &quot;%Deserialize:statusCode%&quot;,
    &quot;status&quot; : &quot;%Deserialize:status%&quot;
  },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;useFormulas&quot; : true,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Response Action</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>Upload_Success</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%Deserialize:statusCode% == 201&quot;,
  &quot;show&quot; : null,
  &quot;disOnTplt&quot; : false,
  &quot;isActive&quot; : true,
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
        <sequenceNumber>10.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessKey>UniDocumentale_Creazione</omniProcessKey>
    <omniProcessType>Integration Procedure</omniProcessType>
    <propertySetConfig>{
  &quot;linkToExternalObject&quot; : &quot;&quot;,
  &quot;trackingCustomData&quot; : [ ],
  &quot;includeAllActionsInResponse&quot; : false,
  &quot;columnsPropertyMap&quot; : [ ],
  &quot;relationshipFieldsMap&quot; : [ ],
  &quot;labelSingular&quot; : &quot;&quot;,
  &quot;labelPlural&quot; : &quot;&quot;,
  &quot;description&quot; : &quot;&quot;,
  &quot;nameColumn&quot; : &quot;&quot;,
  &quot;rollbackOnError&quot; : false,
  &quot;chainableQueriesLimit&quot; : 50,
  &quot;chainableDMLStatementsLimit&quot; : null,
  &quot;chainableCpuLimit&quot; : 2000,
  &quot;chainableHeapSizeLimit&quot; : null,
  &quot;chainableDMLRowsLimit&quot; : null,
  &quot;chainableQueryRowsLimit&quot; : null,
  &quot;chainableSoslQueriesLimit&quot; : null,
  &quot;chainableActualTimeLimit&quot; : null,
  &quot;additionalChainableResponse&quot; : [ ],
  &quot;queueableChainableQueriesLimit&quot; : 120,
  &quot;queueableChainableCpuLimit&quot; : 40000,
  &quot;queueableChainableHeapSizeLimit&quot; : 6,
  &quot;ttlMinutes&quot; : 5,
  &quot;mockResponseMap&quot; : [ ],
  &quot;transientValues&quot; : {
    &quot;activateOrDeactivateConsent&quot; : false
  }
}</propertySetConfig>
    <subType>Creazione</subType>
    <type>UniDocumentale</type>
    <uniqueName>UniDocumentale_Creazione_Procedure_16</uniqueName>
    <versionNumber>16.0</versionNumber>
</OmniIntegrationProcedure>
