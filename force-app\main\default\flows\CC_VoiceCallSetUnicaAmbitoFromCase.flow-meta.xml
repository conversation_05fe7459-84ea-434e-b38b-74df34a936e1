<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>64.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_PicklistToText</name>
        <label>Assign_PicklistToText</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>var_CaseAmbitoText</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Case_from_Call.CommercialAreasOfNeed__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_VoiceCall_UnicaAmbito</targetReference>
        </connector>
    </assignments>
    <environments>Default</environments>
    <formulas>
        <name>Formula_UnicaAmbito</name>
        <dataType>String</dataType>
        <expression>&quot;Unica - &quot; &amp; SUBSTITUTE({!var_CaseAmbitoText}, &quot;;&quot;, &quot;; &quot;)</expression>
    </formulas>
    <interviewLabel>CC_VoiceCall_SetUnicaAmbitoFromCase {!$Flow.CurrentDateTime}</interviewLabel>
    <label>CC_VoiceCallSetUnicaAmbitoFromCase</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_Case_from_Call</name>
        <label>Get_Case_from_Call</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_PicklistToText</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$Record.RelatedCase__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>Update_VoiceCall_UnicaAmbito</name>
        <label>Update_VoiceCall_UnicaAmbito</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <inputAssignments>
            <field>CaseChannelOrigin__c</field>
            <value>
                <elementReference>Get_Case_from_Call.Origin</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>CaseType__c</field>
            <value>
                <elementReference>Get_Case_from_Call.Type</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>ProductandScope__c</field>
            <value>
                <elementReference>Formula_UnicaAmbito</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RelatedRecordId</field>
            <value>
                <elementReference>$Record.RelatedCase__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Target__c</field>
            <value>
                <stringValue>Vendita</stringValue>
            </value>
        </inputAssignments>
        <inputReference>$Record</inputReference>
    </recordUpdates>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Case_from_Call</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>RelatedCase__c</field>
            <operator>IsNull</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <object>VoiceCall</object>
        <recordTriggerType>CreateAndUpdate</recordTriggerType>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Obsolete</status>
    <variables>
        <name>var_CaseAmbitoText</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
